# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with the AI Quality Inspection System codebase.

## Project Overview

A specialized Next.js 15 admin dashboard for AI-powered quality inspection of customer service calls. Features multi-role access (agents, team leaders, reviewers, supervisors), real-time quality scoring, appeal workflows, and comprehensive analytics.

## Business Context

### Core System Components
- **Quality Inspection Engine**: AI-driven call analysis with configurable rules
- **Review Workflow**: Multi-stage review process (AI → Manual → Appeal)
- **Role-Based Dashboards**: Tailored interfaces for each user type
- **Appeal Management**: Complete appeal lifecycle management
- **Real-time Monitoring**: Live quality alerts and intervention triggers

### Key User Roles
- **Agents**: View personal quality scores, submit appeals
- **Team Leaders**: Monitor team performance, manage appeals
- **Reviewers**: Conduct manual quality reviews
- **Supervisors**: System-wide oversight and configuration

## Development Commands

### Essential Commands
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run lint         # ESLint with project rules
npm run format       # Prettier formatting
npx shadcn@latest add [component]  # Add UI component
```

### Quality Checks
```bash
npm run format:check  # Check formatting
npm run lint --fix    # Auto-fix ESLint issues
```

## Architecture & File Structure

### Demo Mode Pages (Primary Development Area)
```
src/app/(main)/demo/
├── agent-dashboard/         # Agent perspective
├── team-leader-dashboard/   # Team leader view
├── reviewer-dashboard/      # Reviewer workspace
├── supervisor-dashboard/    # Supervisor overview
├── quality-scores/          # Multi-role score management
├── notification-center/     # Notification management
├── conversation-detail/     # Call detail analysis
└── quality-inspection-detail/ # Full inspection workflow
```

### Component Organization
```
src/app/(main)/demo/[feature]/
├── _components/            # Feature-specific components
│   ├── page-header.tsx    # Consistent page headers
│   ├── metrics-section.tsx # KPI dashboards
│   └── [feature-specific]/ # Domain components
├── page.tsx               # Main page component
└── types.ts               # Feature-specific types
```

## Development Patterns

### Page Development Standards
1. **Directory Structure**: Use kebab-case for all directories
2. **Component Splitting**: Single responsibility, max 100 lines per file
3. **Type Definitions**: Define all data interfaces in dedicated types.ts
4. **Data Mocking**: Use realistic quality inspection data
5. **Responsive Design**: Mobile-first with Tailwind breakpoints

### Quality Inspection Data Structures
```typescript
interface QualityScore {
  id: string
  agentName: string
  customerPhone: string
  callDuration: number
  aiScore: number
  reviewScore?: number
  appealScore?: number
  finalScore: number
  qualityResult: "合格" | "不合格"
  appealStatus: "未申诉" | "申诉中" | "申诉成功" | "申诉失败"
}

interface InspectionRule {
  id: string
  ruleName: string
  description: string
  maxScore: number
  actualScore: number
  isHit: boolean
  triggerTime?: number
}
```

### Dashboard Layout Patterns
- **Page Header**: Title, subtitle, user context, status badges
- **Metrics Grid**: 2x2 or 3x2 cards with trend indicators
- **Chart Sections**: Recharts integration with CSS variables
- **Data Tables**: TanStack Table with sorting/filtering
- **Action Panels**: Context-aware operation buttons

## UI Components & Styling

### Quality System Components
- **MetricCard**: KPI display with trend indicators
- **ScoreBadge**: Quality score visualization
- **AppealStatus**: Appeal state indicators
- **CallDetailPanel**: Audio + transcript integration
- **ProcessTimeline**: Review workflow visualization

### Design System Usage
- **Colors**: Semantic color coding for quality states
  - Green: Qualified/Approved
  - Red: Failed/Rejected  
  - Yellow: Under Review/Pending
  - Blue: Information/Processing
- **Icons**: Consistent iconography from Lucide React
- **Spacing**: 8px grid system with Tailwind

## Data Mocking Strategy

### Realistic Test Data
```typescript
// Generate inspection scores
const mockScores = Array.from({ length: 50 }, (_, i) => ({
  id: `QS_${String(i + 1).padStart(4, '0')}`,
  agentName: `Agent_${String.fromCharCode(65 + (i % 26))}${Math.floor(i / 26) + 1}`,
  customerPhone: `138****${String(1000 + i).slice(-4)}`,
  callDuration: Math.floor(Math.random() * 300) + 60,
  aiScore: Math.floor(Math.random() * 40) + 60,
  finalScore: Math.floor(Math.random() * 30) + 70,
  // ... other fields
}))
```

## Role-Based Development

### Agent Dashboard
- Personal performance metrics
- Appeal submission workflow
- Recent quality scores
- Notification center

### Team Leader Dashboard  
- Team performance overview
- Member ranking and trends
- Appeal management for team
- Error analysis by member

### Reviewer Dashboard
- Pending review queue
- Review workload analytics
- AI accuracy metrics
- Review history tracking

### Supervisor Dashboard
- System-wide quality metrics
- Team comparisons
- Appeal success rates
- Quality trend analysis

## Development Workflow

### 1. Feature Development
```bash
# Create new page structure
mkdir src/app/(main)/demo/[feature-name]
cd src/app/(main)/demo/[feature-name]
mkdir _components
touch page.tsx types.ts

# Start with page header
echo 'export default function PageHeader() { return <div>Header</div> }' > _components/page-header.tsx
```

### 2. Component Patterns
```typescript
// Standard page component structure
interface PageProps {
  searchParams: { [key: string]: string | string[] | undefined }
}

export default function Page({ searchParams }: PageProps) {
  const role = searchParams.role || 'agent'
  return (
    <div className="space-y-6">
      <PageHeader role={role} />
      <MetricsSection role={role} />
      {/* Additional sections */}
    </div>
  )
}
```

### 3. Testing Checklist
- [ ] Directory structure follows kebab-case
- [ ] Components under _components/ directory
- [ ] TypeScript interfaces defined
- [ ] Responsive design tested (mobile/desktop)
- [ ] Dark mode compatibility verified
- [ ] Realistic mock data implemented
- [ ] Role-based permissions handled
- [ ] ESLint and Prettier checks pass

## Configuration Files

### Key Configuration Locations
- **Theme System**: `src/app/globals.css` (CSS variables)
- **Component Registry**: `components.json` (shadcn/ui)
- **ESLint Rules**: `eslint.config.mjs` (project-specific)
- **TypeScript**: `tsconfig.json` (strict mode)

## Common Gotchas

### Development Pitfalls
1. **Hardcoded Values**: Always use CSS variables for colors/spacing
2. **Missing Types**: Define interfaces before implementation
3. **Large Components**: Split complex UIs into smaller components
4. **Role Logic**: Centralize role-based logic in utility functions
5. **Mock Data**: Ensure data reflects real inspection scenarios

### Performance Considerations
- Use React Server Components where possible
- Implement proper loading states
- Optimize table rendering for large datasets
- Cache computed metrics appropriately