"use client"

import { TrendingUp, TrendingDown, Minus } from "lucide-react"
import { Card, CardAction, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

interface TeamMetricData {
  title: string
  value: string
  trend: "up" | "down" | "stable"
  trendValue: string
  description: string
  footer: string
}

const teamMetricsData: TeamMetricData[] = [
  { 
    title: "团队平均质检得分", 
    value: "85.2", 
    trend: "up", 
    trendValue: "+2.1%", 
    description: "本周趋势上升",
    footer: "较上周提升2.1分"
  },
  { 
    title: "团队质检合格率", 
    value: "92.1%", 
    trend: "up", 
    trendValue: "+1.8%", 
    description: "合格率提升",
    footer: "团队整体表现良好"
  },
  { 
    title: "团队申诉率", 
    value: "3.1%", 
    trend: "down", 
    trendValue: "-0.5%", 
    description: "申诉率下降",
    footer: "质检结果认可度提高"
  },
  { 
    title: "团队申诉成功率", 
    value: "15.2%", 
    trend: "stable", 
    trendValue: "0%", 
    description: "申诉成功率稳定",
    footer: "维持在合理水平"
  }
]

/**
 * 团队核心绩效指标组件
 * 展示团队维度的关键KPI指标
 */
export function TeamMetricsSection() {
  const getTrendIcon = (trend: TeamMetricData["trend"]) => {
    switch (trend) {
      case "up":
        return <TrendingUp className="size-4" />
      case "down":
        return <TrendingDown className="size-4" />
      default:
        return <Minus className="size-4" />
    }
  }

  return (
    <div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs md:grid-cols-2 lg:grid-cols-4">
      {teamMetricsData.map((metric, index) => (
        <Card key={index} className="@container/card">
          <CardHeader>
            <CardDescription>{metric.title}</CardDescription>
            <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
              {metric.value}
            </CardTitle>
            <CardAction>
              <Badge variant="outline">
                {getTrendIcon(metric.trend)}
                {metric.trendValue}
              </Badge>
            </CardAction>
          </CardHeader>
          <CardFooter className="flex-col items-start gap-1.5 text-sm">
            <div className="line-clamp-1 flex gap-2 font-medium">
              {metric.description} {getTrendIcon(metric.trend)}
            </div>
            <div className="text-muted-foreground">{metric.footer}</div>
          </CardFooter>
        </Card>
      ))}
    </div>
  )
}
