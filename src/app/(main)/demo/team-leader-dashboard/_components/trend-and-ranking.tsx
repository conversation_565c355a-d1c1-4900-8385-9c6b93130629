"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ianG<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"
import { Trophy, Medal, Award, TrendingUp, TrendingDown, Minus } from "lucide-react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { Badge } from "@/components/ui/badge"

/**
 * 团队趋势数据接口
 */
interface TeamTrendData {
  date: string
  teamScore: number
}

/**
 * 团队成员数据接口
 */
interface TeamMember {
  rank: number
  name: string
  score: number
  passRate: number
  appealCount: number
  trend: "up" | "down" | "stable"
}

/**
 * 模拟团队趋势数据
 */
const trendData: TeamTrendData[] = [
  { date: "2024-01-08", teamScore: 86.5 },
  { date: "2024-01-09", teamScore: 87.2 },
  { date: "2024-01-10", teamScore: 85.8 },
  { date: "2024-01-11", teamScore: 88.1 },
  { date: "2024-01-12", teamScore: 89.3 },
  { date: "2024-01-13", teamScore: 87.9 },
  { date: "2024-01-14", teamScore: 90.2 },
  { date: "2024-01-15", teamScore: 88.7 },
  { date: "2024-01-16", teamScore: 89.5 },
  { date: "2024-01-17", teamScore: 91.1 },
  { date: "2024-01-18", teamScore: 90.4 },
  { date: "2024-01-19", teamScore: 92.3 },
  { date: "2024-01-20", teamScore: 89.8 },
  { date: "2024-01-21", teamScore: 88.6 }
]

/**
 * 模拟团队成员数据
 */
const teamMembers: TeamMember[] = [
  { rank: 1, name: "张小明", score: 92.5, passRate: 96.8, appealCount: 2, trend: "up" },
  { rank: 2, name: "李小红", score: 91.2, passRate: 95.4, appealCount: 3, trend: "stable" },
  { rank: 3, name: "王小强", score: 89.8, passRate: 93.2, appealCount: 4, trend: "up" },
  { rank: 4, name: "赵小美", score: 88.5, passRate: 91.7, appealCount: 5, trend: "down" },
  { rank: 5, name: "陈小华", score: 87.3, passRate: 90.1, appealCount: 6, trend: "stable" }
]

const chartConfig = {
  teamScore: {
    label: "团队平均分",
    color: "var(--chart-1)",
  },
} satisfies ChartConfig

/**
 * 团队趋势与排名组件
 * 展示团队成绩趋势和成员排名
 */
export function TrendAndRanking() {
  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Trophy className="size-4 text-yellow-500" />
      case 2:
        return <Medal className="size-4 text-gray-400" />
      case 3:
        return <Award className="size-4 text-amber-600" />
      default:
        return <span className="size-4 flex items-center justify-center text-xs font-bold text-muted-foreground">#{rank}</span>
    }
  }

  const getTrendIcon = (trend: TeamMember["trend"]) => {
    switch (trend) {
      case "up":
        return <TrendingUp className="size-3 text-green-500" />
      case "down":
        return <TrendingDown className="size-3 text-red-500" />
      default:
        return <Minus className="size-3 text-muted-foreground" />
    }
  }

  const handleMemberClick = (member: TeamMember) => {
    console.log("查看成员详情:", member)
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* 左侧：团队成绩趋势图 */}
      <div className="lg:col-span-2">
        <Card className="@container/card">
          <CardHeader>
            <CardTitle>团队成绩趋势分析</CardTitle>
          </CardHeader>
          <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
            <ChartContainer config={chartConfig} className="aspect-auto h-[250px] w-full">
              <LineChart data={trendData}>
                <CartesianGrid vertical={false} />
                <XAxis
                  dataKey="date"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                  minTickGap={32}
                  tickFormatter={(value) => {
                    const date = new Date(value);
                    return date.toLocaleDateString("zh-CN", {
                      month: "short",
                      day: "numeric",
                    });
                  }}
                />
                <ChartTooltip
                  cursor={false}
                  content={
                    <ChartTooltipContent
                      labelFormatter={(value) => {
                        return new Date(value).toLocaleDateString("zh-CN", {
                          year: "numeric",
                          month: "long",
                          day: "numeric",
                        });
                      }}
                      indicator="dot"
                    />
                  }
                />
                <Line
                  dataKey="teamScore"
                  type="monotone"
                  stroke="var(--color-teamScore)"
                  strokeWidth={2}
                  dot={{
                    fill: "var(--color-teamScore)",
                    strokeWidth: 2,
                    r: 4,
                  }}
                  activeDot={{
                    r: 6,
                  }}
                />
              </LineChart>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>

      {/* 右侧：团队成员排名 */}
      <div>
        <Card>
          <CardHeader>
            <CardTitle>团队成员排名</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {teamMembers.map((member) => (
                <div 
                  key={member.rank} 
                  className="flex items-center justify-between p-3 rounded-lg border hover:bg-muted/50 transition-colors cursor-pointer"
                  onClick={() => handleMemberClick(member)}
                >
                  <div className="flex items-center gap-3">
                    {getRankIcon(member.rank)}
                    <div>
                      <div className="font-medium text-sm flex items-center gap-2">
                        {member.name}
                        {getTrendIcon(member.trend)}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        合格率: {member.passRate}% | 申诉: {member.appealCount}次
                      </div>
                    </div>
                  </div>
                  <Badge variant={member.score >= 90 ? "default" : "secondary"}>
                    {member.score}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
