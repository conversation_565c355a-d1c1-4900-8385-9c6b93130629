"use client"

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, TrendingUp, TrendingDown, Minus } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

interface ErrorItem {
  id: string
  name: string
  count: number
  severity: "high" | "medium" | "low"
  trend: "up" | "down" | "stable"
}

const severeErrors: ErrorItem[] = [
  { id: "1", name: "服务态度恶劣", count: 12, severity: "high", trend: "down" },
  { id: "2", name: "泄露客户信息", count: 8, severity: "high", trend: "stable" },
  { id: "3", name: "违规操作系统", count: 6, severity: "high", trend: "up" },
  { id: "4", name: "恶意挂断电话", count: 5, severity: "high", trend: "down" },
  { id: "5", name: "拒绝提供服务", count: 3, severity: "high", trend: "stable" }
]

const frequentErrors: <PERSON><PERSON>r<PERSON><PERSON>[] = [
  { id: "1", name: "未及时回应客户", count: 45, severity: "medium", trend: "up" },
  { id: "2", name: "专业知识不足", count: 38, severity: "medium", trend: "down" },
  { id: "3", name: "语言表达不当", count: 32, severity: "medium", trend: "stable" },
  { id: "4", name: "流程执行不规范", count: 28, severity: "medium", trend: "up" },
  { id: "5", name: "记录信息不完整", count: 24, severity: "low", trend: "down" }
]

/**
 * 错误分析统计组件
 * 展示团队的严重错误项和高频失分项
 */
export function ErrorAnalysis() {
  const getSeverityColor = (severity: ErrorItem["severity"]) => {
    switch (severity) {
      case "high":
        return "destructive"
      case "medium":
        return "secondary"
      default:
        return "outline"
    }
  }

  const getTrendIcon = (trend: ErrorItem["trend"]) => {
    switch (trend) {
      case "up":
        return <TrendingUp className="size-3 text-red-500" />
      case "down":
        return <TrendingDown className="size-3 text-green-500" />
      default:
        return <Minus className="size-3 text-muted-foreground" />
    }
  }

  const handleErrorClick = (error: ErrorItem) => {
    console.log("查看错误详情:", error)
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* 左侧：团队严重错误项 Top 5 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="size-5 text-red-500" />
            团队严重错误项 Top 5
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {severeErrors.map((error, index) => (
              <div 
                key={error.id} 
                className="flex items-center justify-between p-3 rounded-lg hover:bg-muted/50 cursor-pointer transition-colors"
                onClick={() => handleErrorClick(error)}
              >
                <div className="flex items-center gap-3">
                  <span className="flex items-center justify-center size-6 rounded-full bg-muted text-xs font-bold">
                    {index + 1}
                  </span>
                  <span className="font-medium">{error.name}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={getSeverityColor(error.severity)}>
                    {error.count}次
                  </Badge>
                  {getTrendIcon(error.trend)}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 右侧：团队高频失分项 Top 5 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingDown className="size-5 text-orange-500" />
            团队高频失分项 Top 5
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {frequentErrors.map((error, index) => (
              <div 
                key={error.id} 
                className="flex items-center justify-between p-3 rounded-lg hover:bg-muted/50 cursor-pointer transition-colors"
                onClick={() => handleErrorClick(error)}
              >
                <div className="flex items-center gap-3">
                  <span className="flex items-center justify-center size-6 rounded-full bg-muted text-xs font-bold">
                    {index + 1}
                  </span>
                  <span className="font-medium">{error.name}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={getSeverityColor(error.severity)}>
                    {error.count}次
                  </Badge>
                  {getTrendIcon(error.trend)}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}