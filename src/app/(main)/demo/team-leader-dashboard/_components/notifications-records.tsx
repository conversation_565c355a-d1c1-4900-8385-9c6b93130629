"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>t, <PERSON> } from "lucide-react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"

interface Notification {
  id: string
  title: string
  content: string
  type: "info" | "warning" | "success"
  time: string
}

interface QualityRecord {
  id: string
  agentName: string
  customerPhone: string
  startTime: string
  duration: string
  finalScore: number
  result: "pass" | "fail"
  appealStatus: "none" | "pending" | "approved" | "rejected"
}

const notifications: Notification[] = [
  {
    id: "1",
    title: "质检规则更新通知",
    content: "新版质检规则将于明日生效，请及时组织团队学习",
    type: "warning",
    time: "2024-01-21 14:30"
  },
  {
    id: "2", 
    title: "团队表现优秀",
    content: "恭喜A班组本月质检成绩排名第二，继续保持",
    type: "success",
    time: "2024-01-21 10:15"
  },
  {
    id: "3",
    title: "培训安排通知",
    content: "下周三将举行服务技能提升培训，请安排团队成员参加",
    type: "info",
    time: "2024-01-20 16:45"
  }
]

const qualityRecords: QualityRecord[] = [
  {
    id: "QC20240121001",
    agentName: "张小明",
    customerPhone: "138****1234",
    startTime: "2024-01-21 09:15",
    duration: "5分32秒",
    finalScore: 94.8,
    result: "pass",
    appealStatus: "none"
  },
  {
    id: "QC20240121002", 
    agentName: "李小红",
    customerPhone: "139****5678",
    startTime: "2024-01-21 09:32",
    duration: "3分18秒",
    finalScore: 87.2,
    result: "pass",
    appealStatus: "none"
  },
  {
    id: "QC20240121003",
    agentName: "王小强",
    customerPhone: "136****9012",
    startTime: "2024-01-21 10:05",
    duration: "7分45秒",
    finalScore: 72.5,
    result: "fail",
    appealStatus: "pending"
  },
  {
    id: "QC20240121004",
    agentName: "赵小丽", 
    customerPhone: "137****3456",
    startTime: "2024-01-21 10:28",
    duration: "4分12秒",
    finalScore: 89.6,
    result: "pass",
    appealStatus: "none"
  }
]

/**
 * 通知公告与质检记录组件
 * 左侧展示通知公告，右侧展示近期质检记录
 */
export function NotificationsRecords() {
  const getNotificationColor = (type: Notification["type"]) => {
    switch (type) {
      case "warning":
        return "destructive"
      case "success":
        return "default"
      default:
        return "secondary"
    }
  }

  const getResultColor = (result: QualityRecord["result"]) => {
    return result === "pass" ? "default" : "destructive"
  }

  const getAppealStatusText = (status: QualityRecord["appealStatus"]) => {
    switch (status) {
      case "pending":
        return "申诉中"
      case "approved":
        return "申诉成功"
      case "rejected":
        return "申诉失败"
      default:
        return "无申诉"
    }
  }

  const handleRecordClick = (record: QualityRecord) => {
    console.log("查看质检详情:", record)
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* 左侧：通知公告 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="size-5 text-blue-500" />
            通知公告
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {notifications.map((notification) => (
              <div key={notification.id} className="p-3 rounded-lg border hover:bg-muted/50 transition-colors">
                <div className="flex items-start justify-between mb-2">
                  <h4 className="font-medium text-sm">{notification.title}</h4>
                  <Badge variant={getNotificationColor(notification.type)} className="text-xs">
                    {notification.type === "warning" ? "重要" : notification.type === "success" ? "好消息" : "通知"}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground mb-2">{notification.content}</p>
                <div className="text-xs text-muted-foreground">{notification.time}</div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 右侧：近期质检记录 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="size-5 text-green-500" />
            近期质检记录
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {qualityRecords.map((record) => (
              <div key={record.id} className="p-3 rounded-lg border hover:bg-muted/50 transition-colors">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-sm">{record.agentName}</span>
                    <Badge variant={getResultColor(record.result)} className="text-xs">
                      {record.finalScore}分
                    </Badge>
                  </div>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => handleRecordClick(record)}
                  >
                    <Eye className="size-4" />
                  </Button>
                </div>
                <div className="text-xs text-muted-foreground space-y-1">
                  <div>客户：{record.customerPhone} | 时长：{record.duration}</div>
                  <div>时间：{record.startTime} | 申诉：{getAppealStatusText(record.appealStatus)}</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}