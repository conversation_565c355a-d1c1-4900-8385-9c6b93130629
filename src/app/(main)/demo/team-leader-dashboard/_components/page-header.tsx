"use client"

import { Users } from "lucide-react"
import { Badge } from "@/components/ui/badge"

/**
 * 页面头部组件
 * 展示班组长的基本信息和团队状态
 */
export function PageHeader() {
  return (
    <div className="mb-8">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Users className="size-8 text-primary" />
            <div>
              <h1 className="text-3xl font-bold">班组长视角</h1>
              <p className="text-muted-foreground mt-2">
                欢迎回来，李班长 | 客服部-A班组 | 团队成员：12人
              </p>
            </div>
          </div>
        </div>
        <Badge variant="secondary" className="text-lg px-4 py-2">
          排名 #2
        </Badge>
      </div>
    </div>
  )
}