/**
 * 绩效指标数据接口
 */
export interface MetricData {
  title: string
  value: string
  trend: "up" | "down" | "stable"
  trendValue: string
}

/**
 * 团队排名数据接口
 */
export interface TeamRanking {
  rank: number
  name: string
  score: number
  passRate: number
  leader: string
}

/**
 * 坐席排名数据接口
 */
export interface AgentRanking {
  rank: number
  name: string
  team: string
  score: number
  passRate: number
}

/**
 * 错误分析数据接口
 */
export interface ErrorAnalysis {
  id: string
  name: string
  count: number
  severity: "high" | "medium" | "low"
  trend: "up" | "down" | "stable"
}

/**
 * 申诉数据接口
 */
export interface AppealData {
  id: string
  agentName: string
  team: string
  reason: string
  submitTime: string
  status: "pending" | "processing" | "resolved"
}

/**
 * 通知数据接口
 */
export interface NotificationData {
  id: string
  title: string
  content: string
  time: string
  type: "info" | "warning" | "success"
}