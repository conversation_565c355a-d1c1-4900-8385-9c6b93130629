"use client"

import { <PERSON><PERSON><PERSON>riangle, TrendingDown } from "lucide-react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

interface ErrorAnalysis {
  id: string
  name: string
  count: number
  severity: "high" | "medium" | "low"
  trend: "up" | "down" | "stable"
}

const severeErrors: ErrorAnalysis[] = [
  { id: "1", name: "服务态度恶劣", count: 23, severity: "high", trend: "down" },
  { id: "2", name: "泄露客户信息", count: 18, severity: "high", trend: "up" },
  { id: "3", name: "违规承诺", count: 15, severity: "high", trend: "stable" },
  { id: "4", name: "挂断客户电话", count: 12, severity: "medium", trend: "down" },
  { id: "5", name: "未按流程操作", count: 9, severity: "medium", trend: "up" }
]

const frequentErrors: ErrorAnalysis[] = [
  { id: "1", name: "开场白不标准", count: 156, severity: "low", trend: "down" },
  { id: "2", name: "未确认客户信息", count: 134, severity: "medium", trend: "up" },
  { id: "3", name: "结束语不完整", count: 128, severity: "low", trend: "stable" },
  { id: "4", name: "语速过快", count: 98, severity: "low", trend: "down" },
  { id: "5", name: "专业术语使用错误", count: 87, severity: "medium", trend: "up" }
]

/**
 * 错误分析组件
 * 展示严重错误和高频失分项统计
 */
export function ErrorAnalysis() {
  const getSeverityColor = (severity: ErrorAnalysis["severity"]) => {
    switch (severity) {
      case "high":
        return "destructive"
      case "medium":
        return "secondary"
      default:
        return "outline"
    }
  }

  const handleErrorClick = (error: ErrorAnalysis) => {
    console.log("查看错误详情:", error)
  }

  const ErrorCard = ({ 
    title, 
    errors, 
    icon 
  }: { 
    title: string
    errors: ErrorAnalysis[]
    icon: React.ReactNode 
  }) => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          {icon}
          <span>{title}</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {errors.map((error) => (
          <div 
            key={error.id}
            className="flex items-center justify-between p-3 rounded-lg border hover:bg-muted/50 cursor-pointer transition-colors"
            onClick={() => handleErrorClick(error)}
          >
            <div className="flex-1">
              <div className="font-medium">{error.name}</div>
              <div className="text-sm text-muted-foreground mt-1">
                发生次数: {error.count}
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant={getSeverityColor(error.severity)}>
                {error.severity === "high" ? "严重" : error.severity === "medium" ? "中等" : "轻微"}
              </Badge>
              {error.trend === "up" && (
                <TrendingDown className="w-4 h-4 text-red-500" />
              )}
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  )

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <ErrorCard 
        title="严重错误项 Top 5"
        errors={severeErrors}
        icon={<AlertTriangle className="w-5 h-5 text-red-500" />}
      />
      <ErrorCard 
        title="高频失分项 Top 5"
        errors={frequentErrors}
        icon={<TrendingDown className="w-5 h-5 text-orange-500" />}
      />
    </div>
  )
}
