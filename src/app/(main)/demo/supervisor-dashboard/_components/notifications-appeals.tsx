"use client"

import { <PERSON>, MessageSquare, Clock } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

interface NotificationData {
  id: string
  title: string
  content: string
  time: string
  type: "info" | "warning" | "success"
}

interface AppealData {
  id: string
  agentName: string
  team: string
  reason: string
  submitTime: string
  status: "pending" | "processing" | "resolved"
}

const notifications: NotificationData[] = [
  {
    id: "1",
    title: "系统维护通知",
    content: "系统将于今晚22:00-24:00进行维护升级",
    time: "2小时前",
    type: "warning"
  },
  {
    id: "2", 
    title: "质检规则更新",
    content: "客服礼貌用语规则已更新，请及时查看",
    time: "4小时前",
    type: "info"
  },
  {
    id: "3",
    title: "月度质检报告",
    content: "1月份质检报告已生成，请查收",
    time: "1天前",
    type: "success"
  }
]

const appeals: AppealData[] = [
  {
    id: "1",
    agentName: "张小明",
    team: "客服A组",
    reason: "对服务态度评分有异议",
    submitTime: "30分钟前",
    status: "pending"
  },
  {
    id: "2",
    agentName: "李小红", 
    team: "客服B组",
    reason: "认为专业知识扣分不合理",
    submitTime: "1小时前",
    status: "pending"
  },
  {
    id: "3",
    agentName: "王小强",
    team: "技术支持组", 
    reason: "质检结果与实际情况不符",
    submitTime: "2小时前",
    status: "pending"
  }
]

/**
 * 通知与申诉组件
 * 展示系统通知和待处理申诉
 */
export function NotificationsAppeals() {
  const getNotificationColor = (type: NotificationData["type"]) => {
    switch (type) {
      case "warning":
        return "secondary"
      case "success":
        return "default"
      default:
        return "outline"
    }
  }

  const handleAppealProcess = (appeal: AppealData) => {
    console.log("开始处理申诉:", appeal)
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Bell className="w-5 h-5" />
            <span>通知公告</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {notifications.map((notification) => (
            <div key={notification.id} className="p-4 rounded-lg border">
              <div className="flex items-start justify-between mb-2">
                <h4 className="font-medium">{notification.title}</h4>
                <Badge variant={getNotificationColor(notification.type)}>
                  {notification.type === "warning" ? "重要" : 
                   notification.type === "success" ? "完成" : "通知"}
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground mb-2">
                {notification.content}
              </p>
              <div className="flex items-center text-xs text-muted-foreground">
                <Clock className="w-3 h-3 mr-1" />
                {notification.time}
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MessageSquare className="w-5 h-5" />
            <span>待处理申诉</span>
            <Badge variant="destructive">{appeals.length}</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {appeals.map((appeal) => (
            <div key={appeal.id} className="p-4 rounded-lg border">
              <div className="flex items-start justify-between mb-2">
                <div>
                  <h4 className="font-medium">{appeal.agentName}</h4>
                  <p className="text-sm text-muted-foreground">{appeal.team}</p>
                </div>
                <Button 
                  size="sm"
                  onClick={() => handleAppealProcess(appeal)}
                >
                  开始处理
                </Button>
              </div>
              <p className="text-sm mb-2">{appeal.reason}</p>
              <div className="flex items-center text-xs text-muted-foreground">
                <Clock className="w-3 h-3 mr-1" />
                {appeal.submitTime}
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    </div>
  )
}
