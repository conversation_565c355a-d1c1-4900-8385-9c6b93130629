"use client"

import { Trophy, Medal, Award } from "lucide-react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"

interface TeamRanking {
  rank: number
  name: string
  score: number
  passRate: number
  leader: string
}

interface AgentRanking {
  rank: number
  name: string
  team: string
  score: number
  passRate: number
}

const teamRankings: TeamRanking[] = [
  { rank: 1, name: "客服A组", score: 92.3, passRate: 97.8, leader: "张组长" },
  { rank: 2, name: "客服B组", score: 89.7, passRate: 95.2, leader: "李组长" },
  { rank: 3, name: "客服C组", score: 87.1, passRate: 93.6, leader: "王组长" },
  { rank: 4, name: "技术支持组", score: 85.9, passRate: 92.1, leader: "赵组长" },
  { rank: 5, name: "投诉处理组", score: 84.2, passRate: 90.8, leader: "陈组长" }
]

const agentRankings: AgentRanking[] = [
  { rank: 1, name: "张小明", team: "客服A组", score: 96.8, passRate: 100 },
  { rank: 2, name: "李小红", team: "客服A组", score: 95.2, passRate: 98.9 },
  { rank: 3, name: "王小强", team: "客服B组", score: 94.7, passRate: 97.8 },
  { rank: 4, name: "赵小丽", team: "技术支持组", score: 93.1, passRate: 96.7 },
  { rank: 5, name: "陈小华", team: "客服C组", score: 92.5, passRate: 95.6 }
]

/**
 * 排名统计组件
 * 展示团队和坐席的绩效排名
 */
export function RankingSection() {
  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Trophy className="w-4 h-4 text-yellow-500" />
      case 2:
        return <Medal className="w-4 h-4 text-gray-400" />
      case 3:
        return <Award className="w-4 h-4 text-amber-600" />
      default:
        return <span className="w-4 h-4 flex items-center justify-center text-sm font-bold">{rank}</span>
    }
  }

  const handleTeamClick = (team: TeamRanking) => {
    console.log("查看团队详情:", team)
  }

  const handleAgentClick = (agent: AgentRanking) => {
    console.log("查看坐席详情:", agent)
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>团队排名 Top 5</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-16">排名</TableHead>
                <TableHead>团队名称</TableHead>
                <TableHead>平均分</TableHead>
                <TableHead>合格率</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {teamRankings.map((team) => (
                <TableRow 
                  key={team.rank} 
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleTeamClick(team)}
                >
                  <TableCell className="flex items-center">
                    {getRankIcon(team.rank)}
                  </TableCell>
                  <TableCell className="font-medium">{team.name}</TableCell>
                  <TableCell>{team.score}</TableCell>
                  <TableCell>
                    <Badge variant={team.passRate >= 95 ? "default" : "secondary"}>
                      {team.passRate}%
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>坐席排名 Top 5</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-16">排名</TableHead>
                <TableHead>姓名</TableHead>
                <TableHead>团队</TableHead>
                <TableHead>得分</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {agentRankings.map((agent) => (
                <TableRow 
                  key={agent.rank}
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleAgentClick(agent)}
                >
                  <TableCell className="flex items-center">
                    {getRankIcon(agent.rank)}
                  </TableCell>
                  <TableCell className="font-medium">{agent.name}</TableCell>
                  <TableCell className="text-sm text-muted-foreground">
                    {agent.team}
                  </TableCell>
                  <TableCell>
                    <Badge variant={agent.score >= 95 ? "default" : "secondary"}>
                      {agent.score}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
