"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, <PERSON>A<PERSON>s } from "recharts"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

/**
 * 趋势数据接口
 */
interface TrendData {
  date: string
  avgScore: number
  passRate: number
}

/**
 * 模拟趋势数据
 */
const trendData: TrendData[] = [
  { date: "2024-01-08", avgScore: 85.2, passRate: 89.5 },
  { date: "2024-01-09", avgScore: 86.8, passRate: 91.2 },
  { date: "2024-01-10", avgScore: 84.9, passRate: 88.7 },
  { date: "2024-01-11", avgScore: 87.5, passRate: 92.1 },
  { date: "2024-01-12", avgScore: 88.3, passRate: 93.4 },
  { date: "2024-01-13", avgScore: 86.7, passRate: 90.8 },
  { date: "2024-01-14", avgScore: 89.1, passRate: 94.2 },
  { date: "2024-01-15", avgScore: 87.9, passRate: 91.6 },
  { date: "2024-01-16", avgScore: 88.6, passRate: 92.8 },
  { date: "2024-01-17", avgScore: 90.2, passRate: 95.1 },
  { date: "2024-01-18", avgScore: 89.4, passRate: 93.7 },
  { date: "2024-01-19", avgScore: 91.1, passRate: 96.2 },
  { date: "2024-01-20", avgScore: 88.8, passRate: 92.3 },
  { date: "2024-01-21", avgScore: 87.5, passRate: 91.0 }
]

const chartConfig = {
  avgScore: {
    label: "平均分",
    color: "var(--chart-1)",
  },
  passRate: {
    label: "合格率", 
    color: "var(--chart-2)",
  },
} satisfies ChartConfig

/**
 * 成绩趋势分析组件
 * 展示整体平均分和合格率的变化趋势
 */
export function TrendChart() {
  return (
    <Card className="@container/card">
      <CardHeader>
        <CardTitle>成绩趋势分析</CardTitle>
      </CardHeader>
      <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
        <ChartContainer config={chartConfig} className="aspect-auto h-[250px] w-full">
          <LineChart data={trendData}>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={(value) => {
                const date = new Date(value);
                return date.toLocaleDateString("zh-CN", {
                  month: "short",
                  day: "numeric",
                });
              }}
            />
            <ChartTooltip
              cursor={false}
              content={
                <ChartTooltipContent
                  labelFormatter={(value) => {
                    return new Date(value).toLocaleDateString("zh-CN", {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    });
                  }}
                  indicator="dot"
                />
              }
            />
            <Line
              dataKey="avgScore"
              type="monotone"
              stroke="var(--color-avgScore)"
              strokeWidth={2}
              dot={{
                fill: "var(--color-avgScore)",
                strokeWidth: 2,
                r: 4,
              }}
              activeDot={{
                r: 6,
              }}
            />
            <Line
              dataKey="passRate"
              type="monotone"
              stroke="var(--color-passRate)"
              strokeWidth={2}
              dot={{
                fill: "var(--color-passRate)",
                strokeWidth: 2,
                r: 4,
              }}
              activeDot={{
                r: 6,
              }}
            />
          </LineChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}





