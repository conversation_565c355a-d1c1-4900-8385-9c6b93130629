"use client"

import { TrendingUp, TrendingDown, Minus } from "lucide-react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

interface MetricData {
  title: string
  value: string
  trend: "up" | "down" | "stable"
  trendValue: string
  description: string
  footer: string
}

const metricsData: MetricData[] = [
  { 
    title: "总质检量", 
    value: "12,456", 
    trend: "up", 
    trendValue: "+8.2%", 
    description: "本月趋势上升",
    footer: "较上月增长8.2%"
  },
  { 
    title: "总复核量", 
    value: "2,341", 
    trend: "down", 
    trendValue: "-3.1%", 
    description: "本月复核量下降",
    footer: "人工复核需求减少"
  },
  { 
    title: "质检平均分", 
    value: "87.5", 
    trend: "up", 
    trendValue: "+2.3%", 
    description: "服务质量提升",
    footer: "整体表现良好"
  },
  { 
    title: "质检合格率", 
    value: "94.2%", 
    trend: "stable", 
    trendValue: "0%", 
    description: "稳定的合格率",
    footer: "符合预期目标"
  },
  { 
    title: "申诉率", 
    value: "5.8%", 
    trend: "down", 
    trendValue: "-1.2%", 
    description: "申诉率下降",
    footer: "质检规则认可度提高"
  },
  { 
    title: "申诉成功率", 
    value: "23.4%", 
    trend: "up", 
    trendValue: "+4.1%", 
    description: "申诉成功率上升",
    footer: "需关注质检规则调整"
  },
  { 
    title: "AI质检准确率", 
    value: "95.7%", 
    trend: "up", 
    trendValue: "+1.8%", 
    description: "AI准确率提升",
    footer: "模型迭代效果显著"
  }
]

/**
 * 核心绩效指标组件
 * 展示质检体系的关键KPI指标
 */
export function MetricsSection() {
  const getTrendIcon = (trend: MetricData["trend"]) => {
    switch (trend) {
      case "up":
        return <TrendingUp className="size-4" />
      case "down":
        return <TrendingDown className="size-4" />
      default:
        return <Minus className="size-4" />
    }
  }

  // 将指标数据分为两行
  const firstRowMetrics = metricsData.slice(0, 4); // 第一行4个指标
  const secondRowMetrics = metricsData.slice(4); // 第二行3个指标

  return (
    <div className="space-y-4">
      {/* 第一行指标 - 4个 */}
      <div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs md:grid-cols-2 lg:grid-cols-4">
        {firstRowMetrics.map((metric, index) => (
          <Card key={index} className="@container/card">
            <CardHeader>
              <CardDescription>{metric.title}</CardDescription>
              <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
                {metric.value}
              </CardTitle>
              <CardAction>
                <Badge variant="outline">
                  {getTrendIcon(metric.trend)}
                  {metric.trendValue}
                </Badge>
              </CardAction>
            </CardHeader>
            <CardFooter className="flex-col items-start gap-1.5 text-sm">
              <div className="line-clamp-1 flex gap-2 font-medium">
                {metric.description} {getTrendIcon(metric.trend)}
              </div>
              <div className="text-muted-foreground">{metric.footer}</div>
            </CardFooter>
          </Card>
        ))}
      </div>

      {/* 第二行指标 - 3个 */}
      <div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs md:grid-cols-3 lg:grid-cols-3">
        {secondRowMetrics.map((metric, index) => (
          <Card key={index} className="@container/card">
            <CardHeader>
              <CardDescription>{metric.title}</CardDescription>
              <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
                {metric.value}
              </CardTitle>
              <CardAction>
                <Badge variant="outline">
                  {getTrendIcon(metric.trend)}
                  {metric.trendValue}
                </Badge>
              </CardAction>
            </CardHeader>
            <CardFooter className="flex-col items-start gap-1.5 text-sm">
              <div className="line-clamp-1 flex gap-2 font-medium">
                {metric.description} {getTrendIcon(metric.trend)}
              </div>
              <div className="text-muted-foreground">{metric.footer}</div>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  )
}








