'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { PageHeader } from './_components/page-header';
import { CallInfoPanel } from './_components/call-info-panel';
import { TaskInfoPanel } from './_components/task-info-panel';
import { ScorePanel } from './_components/score-panel';
import { AudioPlayer } from './_components/audio-player';
import { TranscriptPanel } from './_components/transcript-panel';
import { ScoringDetailsPanel } from './_components/scoring-details-panel';
import { ReviewPanel } from './_components/review-panel';
import { AppealPanel } from './_components/appeal-panel';
import { PerformanceCheckPanel } from './_components/performance-check-panel';
import { SessionData, ViewMode } from './types';
import { mockSessionData } from './mock-data';

export default function QualityInspectionDetailPage() {
  const searchParams = useSearchParams();
  const [session, setSession] = useState<SessionData>(mockSessionData);
  const [mode, setMode] = useState<ViewMode>('basic_view');

  // 音频播放状态
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);

  useEffect(() => {
    const viewMode = searchParams.get('viewMode') as ViewMode;
    if (viewMode && ['basic_view', 'review', 'appeal_processing', 'performance_check'].includes(viewMode)) {
      setMode(viewMode);
    }
  }, [searchParams]);

  const handleScoreUpdate = (newScore: number) => {
    setSession(prev => ({ ...prev, finalScore: newScore }));
  };

  const handleRuleUpdate = (ruleId: string, newWeight: number) => {
    setSession(prev => ({
      ...prev,
      scoringRules: prev.scoringRules.map(rule =>
        rule.id === ruleId ? { ...rule, weight: newWeight } : rule
      )
    }));
  };

  const handleAppealSubmit = async (appealData: any) => {
    console.log('Submitting appeal:', appealData);
    // 这里可以添加实际的API调用
  };

  const handleProcessAppeal = async (decisionData: any) => {
    console.log('Processing appeal:', decisionData);
    // 这里可以添加实际的API调用
  };

  const handleReviewSubmit = async (review: { rules: any[]; comment: string; finalScore: number }) => {
    console.log('Review submitted:', review);
    setSession(prev => ({
      ...prev,
      reviewScore: review.finalScore,
      finalScore: review.finalScore,
      finalScoreSource: 'review'
    }));
  };

  // 音频播放器处理函数
  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const handleSeek = (time: number) => {
    setCurrentTime(time);
  };

  const handleTimeUpdate = (time: number) => {
    setCurrentTime(time);
  };

  const renderModePanel = () => {
    switch (mode) {
      case 'review':
        return (
          <ReviewPanel
            session={session}
            onSubmitReview={handleReviewSubmit}
          />
        );
      case 'appeal_processing':
        return (
          <AppealPanel
            session={session}
            onProcessAppeal={handleProcessAppeal}
          />
        );
      case 'performance_check':
        return (
          <PerformanceCheckPanel
            session={session}
            onSubmitAppeal={handleAppealSubmit}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <PageHeader
        title={session.customer.phone}
        subtitle={`质检详情 - ${session.agent.name} - ${new Date(session.startTime).toLocaleDateString('zh-CN')}`}
        mode={mode}
      />

      <div className="container mx-auto px-4 py-6 space-y-6">
        {/* 主要内容区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            <TaskInfoPanel session={session} />
            <CallInfoPanel session={session} />
            <AudioPlayer
              audioUrl={session.audioUrl}
              currentTime={currentTime}
              duration={duration}
              isPlaying={isPlaying}
              onPlayPause={handlePlayPause}
              onSeek={handleSeek}
              onTimeUpdate={handleTimeUpdate}
            />
            <TranscriptPanel
              transcript={session.transcript}
              currentTime={currentTime}
              onSeek={handleSeek}
            />
          </div>
          <div className="space-y-6">
            <ScorePanel
              machineScore={session.machineScore}
              reviewScore={session.reviewScore}
              appealScore={session.appealScore}
              finalScore={session.finalScore}
              finalScoreSource={session.finalScoreSource}
              isQualified={session.finalScore >= 60}
            />
            <ScoringDetailsPanel
              rules={session.scoringRules}
              mode={mode}
              onSeek={handleSeek}
            />
            {renderModePanel()}
          </div>
        </div>
      </div>
    </div>
  );
}