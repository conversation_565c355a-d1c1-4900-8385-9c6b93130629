'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { PageHeader } from './_components/page-header';
import { CallInfoPanel } from './_components/call-info-panel';
import { TaskInfoPanel } from './_components/task-info-panel';
import { ScorePanel } from './_components/score-panel';
import { AudioPlayer } from './_components/audio-player';
import { TranscriptPanel } from './_components/transcript-panel';
import { ScoringDetailsPanel } from './_components/scoring-details-panel';
import { ReviewPanel } from './_components/review-panel';
import { AppealPanel } from './_components/appeal-panel';
import { PerformanceCheckPanel } from './_components/performance-check-panel';
import { SessionData, ViewMode } from './_components/types';
import { mockSessionData } from './_components/mock-data';

export default function QualityInspectionDetailPage() {
  const searchParams = useSearchParams();
  const [session, setSession] = useState<SessionData>(mockSessionData);
  const [mode, setMode] = useState<ViewMode>('basic_view');

  useEffect(() => {
    const viewMode = searchParams.get('viewMode') as ViewMode;
    if (viewMode && ['basic_view', 'review', 'appeal_processing', 'performance_check'].includes(viewMode)) {
      setMode(viewMode);
    }
  }, [searchParams]);

  const handleScoreUpdate = (newScore: number) => {
    setSession(prev => ({ ...prev, finalScore: newScore }));
  };

  const handleRuleUpdate = (ruleId: string, newWeight: number) => {
    setSession(prev => ({
      ...prev,
      scoringRules: prev.scoringRules.map(rule =>
        rule.id === ruleId ? { ...rule, weight: newWeight } : rule
      )
    }));
  };

  const handleAppealSubmit = async (appealData: any) => {
    console.log('Submitting appeal:', appealData);
    // 这里可以添加实际的API调用
  };

  const handleProcessAppeal = async (decisionData: any) => {
    console.log('Processing appeal:', decisionData);
    // 这里可以添加实际的API调用
  };

  const renderModePanel = () => {
    switch (mode) {
      case 'review':
        return (
          <ReviewPanel
            session={session}
            onScoreUpdate={handleScoreUpdate}
            onRuleUpdate={handleRuleUpdate}
          />
        );
      case 'appeal_processing':
        return (
          <AppealPanel
            session={session}
            onProcessAppeal={handleProcessAppeal}
          />
        );
      case 'performance_check':
        return (
          <PerformanceCheckPanel
            session={session}
            onSubmitAppeal={handleAppealSubmit}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <PageHeader
        title={session.customer.phone}
        subtitle={`质检详情 - ${session.agent.name} - ${new Date(session.startTime).toLocaleDateString('zh-CN')}`}
        mode={mode}
      />

      <div className="container mx-auto px-4 py-6 space-y-6">
        {/* 顶部信息区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <CallInfoPanel session={session} />
          </div>
          <div>
            <TaskInfoPanel session={session} />
            <ScorePanel session={session} />
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 左侧音频和文本区域 */}
          <div className="lg:col-span-2 space-y-6">
            <AudioPlayer
              audioUrl={session.audioUrl}
              transcript={session.transcript}
            />
            <TranscriptPanel
              transcript={session.transcript}
              scoringRules={session.scoringRules}
            />
          </div>

          {/* 右侧评分详情区域 */}
          <div className="space-y-6">
            <ScoringDetailsPanel
              session={session}
              onRuleUpdate={handleRuleUpdate}
            />
            {renderModePanel()}
          </div>
        </div>
      </div>
    </div>
  );
}