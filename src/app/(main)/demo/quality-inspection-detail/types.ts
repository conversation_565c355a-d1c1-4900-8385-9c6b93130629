// 质检会话详情页面的类型定义

// 查看模式枚举
export type ViewMode = "basic_view" | "review" | "appeal_processing" | "performance_check";

// 坐席信息
export interface Agent {
  id: string;
  name: string;
  teamName: string;
  avatar?: string;
}

// 客户信息
export interface Customer {
  id: string;
  phone: string;
  name?: string;
}

// 评分规则
export interface ScoringRule {
  id: string;
  name: string;
  description: string;
  category: string;
  points: number;
  isHit: boolean;
  hitTimestamp?: number;
  originalHit: boolean;
  reviewedHit?: boolean;
  reviewedPoints?: number;
}

// 对话文本消息
export interface TranscriptMessage {
  id: string;
  speaker: "agent" | "customer";
  text: string;
  timestamp: number;
  duration: number;
  tags?: string[];
  keywords?: string[];
  isHighlighted?: boolean;
}

// 分数演进记录
export interface ScoreEvolution {
  source: "ai" | "review" | "appeal";
  score: number;
  timestamp: Date;
  operator?: string;
  comment?: string;
}

// 处理时间轴事件
export interface ProcessTimelineEvent {
  id: string;
  type: "ai_check" | "review" | "appeal" | "manual_adjust";
  title: string;
  description: string;
  timestamp: Date;
  operator?: string;
  score?: number;
  status: "completed" | "in_progress" | "pending";
}

// 申诉信息
export interface AppealInfo {
  id: string;
  status: "pending" | "approved" | "rejected";
  reason: string;
  evidence?: string;
  createdAt: Date;
  processedAt?: Date;
  processedBy?: string;
  finalScore?: number;
  comment?: string;
}

// 任务信息
export interface TaskInfo {
  id: string;
  name: string;
  status: "pending" | "in_progress" | "completed" | "cancelled";
  progress: number;
  createdBy: string;
  createdAt: Date;
  deadline?: Date;
  type: "manual" | "scheduled" | "random";
  triggerReason?: string;
}

// 主会话数据
export interface SessionData {
  id: string;
  taskId: string;
  agent: Agent;
  customer: Customer;
  startTime: Date;
  endTime: Date;
  duration: number;
  transcript: TranscriptMessage[];
  audioUrl: string;
  
  // 评分相关
  machineScore: number;
  reviewScore?: number;
  appealScore?: number;
  finalScore: number;
  finalScoreSource: "ai" | "review" | "appeal";
  scoringRules: ScoringRule[];
  scoreEvolution: ScoreEvolution[];
  
  // 状态相关
  appealStatus: "not_applicable" | "can_appeal" | "appealed" | "processed";
  appealDeadline?: Date;
  appealInfo?: AppealInfo;
  
  // 任务相关
  taskInfo: TaskInfo;
  
  // 处理记录
  processTimeline: ProcessTimelineEvent[];
}

// 音频播放状态
export interface AudioState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  playbackRate: number;
}

// 组件 Props 类型
export interface PageHeaderProps {
  title: string;
  subtitle?: string;
  onBack?: () => void;
  mode: ViewMode;
}

export interface AudioPlayerProps {
  audioUrl: string;
  currentTime: number;
  duration: number;
  isPlaying: boolean;
  onPlayPause: () => void;
  onSeek: (time: number) => void;
  onTimeUpdate: (time: number) => void;
}

export interface TranscriptPanelProps {
  transcript: TranscriptMessage[];
  currentTime: number;
  onSeek: (time: number) => void;
}

export interface ScorePanelProps {
  machineScore: number;
  reviewScore?: number;
  appealScore?: number;
  finalScore: number;
  finalScoreSource: "ai" | "review" | "appeal";
  isQualified: boolean;
}

export interface ScoringDetailsPanelProps {
  rules: ScoringRule[];
  mode: ViewMode;
  onRuleChange?: (ruleId: string, changes: Partial<ScoringRule>) => void;
  onSeek?: (timestamp: number) => void;
}

export interface ProcessTimelineProps {
  events: ProcessTimelineEvent[];
}

export interface OperationPanelProps {
  mode: ViewMode;
  session: SessionData;
  onSubmitReview?: (review: { score: number; rules: ScoringRule[]; comment: string }) => void;
  onSubmitAppeal?: (appeal: { reason: string; evidence?: string }) => void;
  onProcessAppeal?: (decision: { approved: boolean; finalScore?: number; comment: string }) => void;
}

// 审核表单数据
export interface ReviewFormData {
  rules: ScoringRule[];
  comment: string;
  finalScore: number;
}

// 申诉表单数据
export interface AppealFormData {
  reason: string;
  evidence?: string;
}

// 处理申诉表单数据
export interface AppealProcessData {
  approved: boolean;
  finalScore?: number;
  comment: string;
}

// 筛选器类型
export interface FilterState {
  category?: string;
  severity?: "high" | "medium" | "low";
  showPassed?: boolean;
  showFailed?: boolean;
}