import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { cn } from '@//lib/utils';
import { ReviewFormData, SessionData, ScoringRule } from '../types';
import { CheckCircle, AlertTriangle, Clock } from 'lucide-react';

interface ReviewPanelProps {
  session: SessionData;
  onSubmitReview: (review: ReviewFormData) => void;
  onCancel?: () => void;
}

export function ReviewPanel({ session, onSubmitReview, onCancel }: ReviewPanelProps) {
  const [rules, setRules] = useState(session.scoringRules);
  const [comment, setComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const calculateCurrentScore = () => {
    const totalPoints = rules.reduce((sum, rule) => {
      const points = rule.reviewedPoints !== undefined ? rule.reviewedPoints : rule.points;
      return sum + (rule.isHit ? 0 : points);
    }, 0);
    return Math.max(0, 100 + totalPoints);
  };

  const currentScore = calculateCurrentScore();
  const scoreChange = currentScore - session.machineScore;

  const handleRuleChange = (ruleId: string, changes: Partial<ScoringRule>) => {
    setRules(prevRules => 
      prevRules.map(rule => 
        rule.id === ruleId ? { ...rule, ...changes } : rule
      )
    );
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    try {
      const reviewData: ReviewFormData = {
        rules,
        comment,
        finalScore: currentScore
      };
      
      await onSubmitReview(reviewData);
    } catch (error) {
      console.error('Failed to submit review:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600 dark:text-green-400';
    if (score >= 80) return 'text-blue-600 dark:text-blue-400';
    if (score >= 60) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getScoreBadgeColor = (score: number) => {
    if (score >= 80) return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
    return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
  };

  const RuleReviewItem = ({ rule }: { rule: ScoringRule }) => (
    <div className="border rounded-lg p-4 transition-all duration-200 hover:shadow-sm">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <h4 className="font-medium text-sm">{rule.name}</h4>
            <Badge variant="outline" className="text-xs">
              {rule.category}
            </Badge>
          </div>
          <p className="text-sm text-muted-foreground">{rule.description}</p>
        </div>
        
        <div className="ml-4 flex items-center gap-2">
          <div className="text-right">
            <div className="text-sm font-medium">
              AI: {rule.originalHit ? '通过' : '未通过'}
            </div>
            <div className="text-sm text-muted-foreground">
              {rule.points}分
            </div>
          </div>
        </div>
      </div>

      <div className="mt-3 pt-3 border-t">
        <div className="flex items-center gap-4">
          <label className="text-sm font-medium">人工复核结果:</label>
          <div className="flex items-center gap-2">
            <label className="flex items-center gap-1">
              <input
                type="radio"
                checked={rule.reviewedHit === true}
                onChange={() => handleRuleChange(rule.id, {
                  reviewedHit: true,
                  reviewedPoints: 0,
                  isHit: true
                })}
                className="w-4 h-4"
              />
              <span className="text-sm">通过</span>
            </label>
            <label className="flex items-center gap-1">
              <input
                type="radio"
                checked={rule.reviewedHit === false}
                onChange={() => handleRuleChange(rule.id, {
                  reviewedHit: false,
                  reviewedPoints: rule.points,
                  isHit: false
                })}
                className="w-4 h-4"
              />
              <span className="text-sm">未通过</span>
            </label>
          </div>
        </div>

        {rule.reviewedHit === false && (
          <div className="mt-2 flex items-center gap-2">
            <label className="text-sm">扣分值:</label>
            <input
              type="number"
              value={rule.reviewedPoints || rule.points}
              onChange={(e) => {
                const points = parseInt(e.target.value) || 0;
                handleRuleChange(rule.id, {
                  reviewedPoints: points,
                  isHit: points === 0
                });
              }}
              className="w-16 text-sm border rounded px-2 py-1"
              min={-100}
              max={0}
            />
          </div>
        )}
      </div>
    </div>
  );

  return (
    <Card className="bg-gradient-to-br from-background to-muted/20 border-muted-foreground/20">
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <CheckCircle className="h-5 w-5" />
          质检复核
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* 分数对比 */}
        <div className="grid grid-cols-3 gap-4 p-4 bg-muted/50 rounded-lg">
          <div className="text-center">
            <div className="text-2xl font-bold text-muted-foreground">
              {session.machineScore}
            </div>
            <div className="text-sm text-muted-foreground">AI初检</div>
          </div>
          
          <div className="text-center">
            <div className={cn("text-2xl font-bold", getScoreColor(currentScore))}>
              {currentScore}
            </div>
            <div className="text-sm">复核后</div>
          </div>
          
          <div className="text-center">
            <Badge className={getScoreBadgeColor(currentScore)}>
              {currentScore >= 80 ? '合格' : '不合格'}
            </Badge>
          </div>
        </div>

        {/* 分数变化提示 */}
        {scoreChange !== 0 && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              分数变化: 
              <span className={cn("font-bold ml-1", scoreChange > 0 ? 'text-green-600' : 'text-red-600')}>
                {scoreChange > 0 ? '+' : ''}{scoreChange}分
              </span>
            </AlertDescription>
          </Alert>
        )}

        {/* 规则复核列表 */}
        <div className="space-y-4">
          <div className="text-sm font-medium text-muted-foreground">
            请逐项复核以下规则:
          </div>
          
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {rules.map(rule => (
              <RuleReviewItem key={rule.id} rule={rule} />
            ))}
          </div>
        </div>

        {/* 复核意见 */}
        <div className="space-y-2">
          <label className="text-sm font-medium">复核意见:</label>
          <Textarea
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            placeholder="请填写您的复核意见..."
            rows={4}
            className="resize-none"
          />
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-3">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              取消
            </Button>
          )}
          
          <Button
            type="button"
            onClick={handleSubmit}
            disabled={isSubmitting || !comment.trim()}
          >
            {isSubmitting ? (
              <>
                <Clock className="h-4 w-4 mr-2 animate-spin" />
                提交中...
              </>
            ) : (
              '提交复核结果'
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}