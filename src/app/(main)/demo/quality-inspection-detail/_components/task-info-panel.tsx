import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { SessionData } from '../types';
import { 
  ClipboardList, 
  User, 
  Calendar, 
  Clock, 
  Target 
} from 'lucide-react';

interface TaskInfoPanelProps {
  session: SessionData;
}

export function TaskInfoPanel({ session }: TaskInfoPanelProps) {
  const getTaskStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300',
      in_progress: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
      completed: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
      cancelled: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
    };
    return colors[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
  };

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'bg-green-500';
    if (progress >= 50) return 'bg-blue-500';
    if (progress >= 20) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Card className="bg-gradient-to-br from-background to-muted/20 border-muted-foreground/20">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          <span className="text-2xl">📋</span>
          任务信息
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 任务状态 */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <ClipboardList className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">任务状态</span>
            </div>
            
            <Badge className={getTaskStatusColor(session.taskInfo.status)}>
              {session.taskInfo.status === 'completed' ? '已完成' :
               session.taskInfo.status === 'in_progress' ? '进行中' :
               session.taskInfo.status === 'pending' ? '待处理' : '已取消'}
            </Badge>
          </div>

          <div className="w-full bg-muted rounded-full h-2">
            <div
              className={cn(
                "h-2 rounded-full transition-all duration-300",
                getProgressColor(session.taskInfo.progress)
              )}
              style={{ width: `${session.taskInfo.progress}%` }}
            />
          </div>
          
          <div className="text-xs text-center text-muted-foreground">
            {session.taskInfo.progress}% 完成
          </div>
        </div>

        {/* 任务基本信息 */}
        <div className="grid grid-cols-2 gap-4 pt-4 border-t border-muted-foreground/10">
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-muted-foreground" />
              <div>
                <div className="text-xs text-muted-foreground">任务名称</div>
                <div className="text-sm font-medium">{session.taskInfo.name}</div>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <div>
                <div className="text-xs text-muted-foreground">创建人</div>
                <div className="text-sm font-medium">{session.taskInfo.createdBy}</div>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <div>
                <div className="text-xs text-muted-foreground">创建时间</div>
                <div className="text-sm font-medium">
                  {formatDate(session.taskInfo.createdAt)}
                </div>
              </div>
            </div>

            {session.taskInfo.deadline && (
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="text-xs text-muted-foreground">截止时间</div>
                  <div className="text-sm font-medium">
                    {formatDate(session.taskInfo.deadline)}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 触发原因 */}
        {session.taskInfo.triggerReason && (
          <div className="pt-4 border-t border-muted-foreground/10">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">触发原因:</span>
              <Badge variant="outline" className="text-xs">
                {session.taskInfo.triggerReason}
              </Badge>
            </div>
          </div>
        )}

        {/* 评分标准 */}
        <div className="pt-4 border-t border-muted-foreground/10">
          <div className="text-sm font-medium mb-2">评分标准</div>
          
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-green-500 rounded-full" />
              <span>优秀: ≥90分</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-blue-500 rounded-full" />
              <span>合格: ≥80分</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-yellow-500 rounded-full" />
              <span>待改进: ≥60分</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-red-500 rounded-full" />
              <span>不合格: &lt;60分</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}