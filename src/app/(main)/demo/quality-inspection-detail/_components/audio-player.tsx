import { useState, useRef, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { cn } from '@/lib/utils';
import { 
  Play, 
  Pause, 
  Rewind, 
  FastForward, 
  Volume2, 
  VolumeX 
} from 'lucide-react';
import { AudioPlayerProps } from '../types';

export function AudioPlayer({
  audioUrl,
  currentTime,
  duration,
  isPlaying,
  onPlayPause,
  onSeek,
  onTimeUpdate
}: AudioPlayerProps) {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleLoadedMetadata = () => {
      if (onTimeUpdate) {
        onTimeUpdate(audio.duration);
      }
    };

    const handleTimeUpdate = () => {
      if (onTimeUpdate) {
        onTimeUpdate(audio.currentTime);
      }
    };

    const handleEnded = () => {
      if (onSeek) {
        onSeek(0);
      }
    };

    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('ended', handleEnded);

    return () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('ended', handleEnded);
    };
  }, [onTimeUpdate, onSeek]);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.play().catch(() => {
        // Handle autoplay restrictions
        console.log('Audio play failed');
      });
    } else {
      audio.pause();
    }
  }, [isPlaying]);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    if (Math.abs(audio.currentTime - currentTime) > 0.5) {
      audio.currentTime = currentTime;
    }
  }, [currentTime]);

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleSeek = (value: number[]) => {
    const newTime = value[0];
    if (audioRef.current) {
      audioRef.current.currentTime = newTime;
    }
    if (onSeek) {
      onSeek(newTime);
    }
  };

  const handleVolumeChange = (value: number[]) => {
    const newVolume = value[0];
    setVolume(newVolume);
    if (audioRef.current) {
      audioRef.current.volume = newVolume;
    }
    setIsMuted(newVolume === 0);
  };

  const handleMuteToggle = () => {
    if (isMuted) {
      setVolume(1);
      if (audioRef.current) {
        audioRef.current.volume = 1;
      }
      setIsMuted(false);
    } else {
      setVolume(0);
      if (audioRef.current) {
        audioRef.current.volume = 0;
      }
      setIsMuted(true);
    }
  };

  const handleSkip = (seconds: number) => {
    const audio = audioRef.current;
    if (!audio) return;

    const newTime = Math.max(0, Math.min(duration, audio.currentTime + seconds));
    audio.currentTime = newTime;
    if (onSeek) {
      onSeek(newTime);
    }
  };

  const progress = duration > 0 ? (currentTime / duration) * 100 : 0;

  return (
    <Card className="bg-gradient-to-br from-background to-muted/20 border-muted-foreground/20">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          <span className="text-2xl">🎧</span>
          音频播放器
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        <audio
          ref={audioRef}
          src={audioUrl}
          preload="metadata"
          className="hidden"
        />

        {/* 播放控制 */}
        <div className="flex items-center justify-center space-x-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => handleSkip(-10)}
            className="hover:scale-110 transition-transform"
          >
            <Rewind className="h-5 w-5" />
          </Button>

          <Button
            variant="default"
            size="lg"
            onClick={onPlayPause}
            className="rounded-full h-14 w-14 hover:scale-110 transition-transform"
          >
            {isPlaying ? (
              <Pause className="h-6 w-6" />
            ) : (
              <Play className="h-6 w-6 ml-1" />
            )}
          </Button>

          <Button
            variant="ghost"
            size="icon"
            onClick={() => handleSkip(10)}
            className="hover:scale-110 transition-transform"
          >
            <FastForward className="h-5 w-5" />
          </Button>
        </div>

        {/* 进度条 */}
        <div className="space-y-2">
          <Slider
            value={[currentTime]}
            max={duration || 100}
            step={0.1}
            onValueChange={handleSeek}
            className="w-full"
          />
          
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>{formatTime(currentTime)}</span>
            <span>{formatTime(duration)}</span>
          </div>

          {/* 进度条可视化 */}
          <div className="relative h-2 w-full bg-muted rounded-full overflow-hidden">
            <div
              className="absolute top-0 left-0 h-full bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-200"
              style={{ width: `${progress}%` }}
            />
            <div
              className="absolute top-0 h-full w-1 bg-white shadow-lg transition-all duration-200"
              style={{ left: `${progress}%` }}
            />
          </div>
        </div>

        {/* 音量控制 */}
        <div className="flex items-center space-x-3 px-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={handleMuteToggle}
            className="h-8 w-8"
          >
            {isMuted ? (
              <VolumeX className="h-4 w-4" />
            ) : (
              <Volume2 className="h-4 w-4" />
            )}
          </Button>

          <Slider
            value={[volume]}
            max={1}
            step={0.1}
            onValueChange={handleVolumeChange}
            className="w-24"
          />

          <span className="text-xs text-muted-foreground w-8 text-right">
            {Math.round(volume * 100)}%
          </span>
        </div>

        {/* 播放状态指示 */}
        <div className="flex items-center justify-center space-x-2">
          <div
            className={cn(
              "h-2 w-2 rounded-full transition-all duration-300",
              isPlaying ? "bg-green-500 animate-pulse" : "bg-muted"
            )}
          />
          <span className="text-sm text-muted-foreground">
            {isPlaying ? '播放中' : '已暂停'}
          </span>
        </div>

        {/* 播放模式提示 */}
        <div className="text-center">
          <div className="text-xs text-muted-foreground">
            音频文件: {audioUrl.split('/').pop()}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}