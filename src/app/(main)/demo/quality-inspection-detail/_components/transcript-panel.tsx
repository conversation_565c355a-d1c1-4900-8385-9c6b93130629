import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { TranscriptPanelProps, TranscriptMessage } from "../types";
import { User, Phone } from "lucide-react";

export function TranscriptPanel({ transcript, currentTime, onSeek }: TranscriptPanelProps) {
  // 计算总时长
  const duration = transcript.length > 0
    ? Math.max(...transcript.map(msg => msg.timestamp + msg.duration))
    : 0;

  const getSpeakerIcon = (speaker: "agent" | "customer") => {
    return speaker === "agent" ? <User className="h-4 w-4" /> : <Phone className="h-4 w-4" />;
  };

  const getSpeakerColor = (speaker: "agent" | "customer") => {
    return speaker === "agent" 
      ? "bg-blue-500 text-white" 
      : "bg-green-500 text-white";
  };

  const getSpeakerLabel = (speaker: "agent" | "customer") => {
    return speaker === "agent" ? "坐席" : "客户";
  };

  const isMessageActive = (message: TranscriptMessage) => {
    const messageEnd = message.timestamp + message.duration;
    return currentTime >= message.timestamp && currentTime <= messageEnd;
  };

  const formatTimestamp = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getTagColor = (tag: string) => {
    const colors: Record<string, string> = {
      '负面情绪': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',
      '关键词': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
      '长时静默': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300',
      '标准开场': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
      '结束语': 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
      '等待': 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
    };
    return colors[tag] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
  };

  return (
    <Card className="bg-gradient-to-br from-background to-muted/20 border-muted-foreground/20">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          <span className="text-2xl">💬</span>
          通话文本
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 头部统计 */}
        <div className="flex justify-between items-center text-sm text-muted-foreground pb-3 border-b border-muted-foreground/10">
          <span>共 {transcript.length} 条消息</span>
          <span>当前时间: {formatTimestamp(currentTime)}</span>
        </div>

        {/* 消息列表 */}
        <div className="space-y-3 max-h-[400px] overflow-y-auto pr-2">
          {transcript.map((message, index) => (
            <div
              key={message.id}
              className={cn(
                "transition-all duration-300",
                isMessageActive(message) && "ring-2 ring-blue-500 rounded-lg"
              )}
            >
              <div
                className={cn(
                  "p-4 rounded-lg cursor-pointer hover:shadow-md transition-all duration-200",
                  "hover:scale-[1.01]",
                  isMessageActive(message) && "bg-blue-50 dark:bg-blue-900/20"
                )}
                onClick={() => onSeek(message.timestamp)}
              >
                <div className="flex items-start space-x-3">
                  {/* 头像 */}
                  <div className={cn(
                    "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center",
                    getSpeakerColor(message.speaker)
                  )}>
                    {getSpeakerIcon(message.speaker)}
                  </div>

                  <div className="flex-1 min-w-0">
                    {/* 发言者信息 */}
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="font-medium text-sm">
                        {getSpeakerLabel(message.speaker)}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {formatTimestamp(message.timestamp)}
                      </span>
                      
                      {isMessageActive(message) && (
                        <span className="text-xs text-blue-600 dark:text-blue-400 animate-pulse">
                          🎵 播放中
                        </span>
                      )}
                    </div>

                    {/* 文本内容 */}
                    <div className="text-sm leading-relaxed">
                      {message.text}
                    </div>

                    {/* 关键词和标签 */}
                    <div className="flex flex-wrap gap-1 mt-2">
                      {message.keywords?.map((keyword, idx) => (
                        <span
                          key={idx}
                          className="text-xs px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 rounded-full"
                        >
                          {keyword}
                        </span>
                      ))}
                      
                      {message.tags?.map((tag, idx) => (
                        <span
                          key={idx}
                          className={cn("text-xs px-2 py-1 rounded-full", getTagColor(tag))}
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* 时间戳按钮 */}
                  <button
                    className="flex-shrink-0 text-xs text-muted-foreground hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                    onClick={(e) => {
                      e.stopPropagation();
                      onSeek(message.timestamp);
                    }}
                  >
                    跳转
                  </button>
                </div>
              </div>

              {/* 连接线 */}
              {index < transcript.length - 1 && (
                <div className="flex justify-center">
                  <div className="w-px h-4 bg-muted-foreground/20" />
                </div>
              )}
            </div>
          ))}
        </div>

        {/* 空状态 */}
        {transcript.length === 0 && (
          <div className="text-center py-8">
            <div className="text-muted-foreground">
              暂无通话文本数据
            </div>
          </div>
        )}

        {/* 快速导航 */}
        <div className="pt-4 border-t border-muted-foreground/10">
          <div className="text-sm font-medium text-muted-foreground mb-2">快速导航</div>
          <div className="flex flex-wrap gap-2">
            <button
              className="text-xs px-3 py-1 bg-muted hover:bg-muted-foreground/20 rounded-md transition-colors"
              onClick={() => onSeek(0)}
            >
              开头
            </button>
            <button
              className="text-xs px-3 py-1 bg-muted hover:bg-muted-foreground/20 rounded-md transition-colors"
              onClick={() => onSeek(Math.floor(duration / 2))}
            >
              中间
            </button>
            <button
              className="text-xs px-3 py-1 bg-muted hover:bg-muted-foreground/20 rounded-md transition-colors"
              onClick={() => onSeek(duration)}
            >
              结尾
            </button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}