import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { cn } from '@/lib/utils';
import { SessionData, AppealFormData } from '../types';
import { AlertTriangle, Clock, Send, CheckCircle } from 'lucide-react';

interface PerformanceCheckPanelProps {
  session: SessionData;
  onSubmitAppeal: (appeal: AppealFormData) => Promise<void>;
}

export function PerformanceCheckPanel({ session, onSubmitAppeal }: PerformanceCheckPanelProps) {
  const [showAppealForm, setShowAppealForm] = useState(false);
  const [reason, setReason] = useState('');
  const [evidence, setEvidence] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const canAppeal = session.appealStatus === 'can_appeal';
  const isAppealed = session.appealStatus === 'appealed';
  const isProcessed = session.appealStatus === 'processed';
  const appealInfo = session.appealInfo;

  const getAppealStatusInfo = () => {
    switch (session.appealStatus) {
      case 'can_appeal':
        return {
          icon: Clock,
          title: '可发起申诉',
          color: 'text-blue-600',
          description: '您可以在申诉截止前发起申诉'
        };
      case 'appealed':
        return {
          icon: AlertTriangle,
          title: '申诉已提交',
          color: 'text-yellow-600',
          description: '您的申诉正在处理中，请耐心等待'
        };
      case 'processed':
        return {
          icon: CheckCircle,
          title: '申诉已处理',
          color: 'text-green-600',
          description: '申诉处理已完成'
        };
      default:
        return {
          icon: Clock,
          title: '不可申诉',
          color: 'text-gray-600',
          description: '当前状态不支持申诉'
        };
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600 dark:text-green-400';
    if (score >= 80) return 'text-blue-600 dark:text-blue-400';
    if (score >= 60) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const handleSubmitAppeal = async () => {
    if (!reason.trim()) return;

    setIsSubmitting(true);
    
    try {
      const appealData: AppealFormData = {
        reason: reason.trim(),
        evidence: evidence.trim()
      };
      
      await onSubmitAppeal(appealData);
      setShowAppealForm(false);
      setReason('');
      setEvidence('');
    } catch (error) {
      console.error('Failed to submit appeal:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getDaysRemaining = () => {
    if (!session.appealDeadline) return 0;
    const now = new Date();
    const deadline = new Date(session.appealDeadline);
    const diffTime = deadline.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  const StatusInfo = getAppealStatusInfo();

  return (
    <Card className="bg-gradient-to-br from-background to-muted/20 border-muted-foreground/20">
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <Clock className="h-5 w-5" />
          申诉操作
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* 申诉状态 */}
        <Alert className={cn(
          "border-l-4",
          session.appealStatus === 'can_appeal' && "border-blue-200 bg-blue-50 dark:border-blue-900 dark:bg-blue-900/20",
          session.appealStatus === 'appealed' && "border-yellow-200 bg-yellow-50 dark:border-yellow-900 dark:bg-yellow-900/20",
          session.appealStatus === 'processed' && "border-green-200 bg-green-50 dark:border-green-900 dark:bg-green-900/20"
        )}>
          <StatusInfo.icon className={cn("h-4 w-4", StatusInfo.color)} />
          <AlertDescription className="space-y-1">
            <div className={cn("font-medium", StatusInfo.color)}>
              {StatusInfo.title}
            </div>
            <div className="text-sm text-muted-foreground">
              {StatusInfo.description}
            </div>
          </AlertDescription>
        </Alert>

        {/* 当前分数 */}
        <div className="grid grid-cols-2 gap-4 p-4 bg-muted/50 rounded-lg">
          <div className="text-center">
            <div className={cn("text-2xl font-bold", getScoreColor(session.finalScore))}>
              {session.finalScore}
            </div>
            <div className="text-sm text-muted-foreground">当前分数</div>
          </div>
          
          <div className="text-center">
            <Badge className={session.finalScore >= 80 ? 
              "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300" : 
              "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
            }>
              {session.finalScore >= 80 ? '合格' : '不合格'}
            </Badge>
            <div className="text-sm text-muted-foreground mt-1">结果状态</div>
          </div>
        </div>

        {/* 申诉截止时间 */}
        {session.appealDeadline && canAppeal && (
          <div className="text-center">
            <div className="text-sm text-muted-foreground">
              申诉截止: {formatDate(session.appealDeadline)}
            </div>
            <div className="text-sm font-medium">
              剩余 {getDaysRemaining()} 天
            </div>
          </div>
        )}

        {/* 申诉表单 */}
        {canAppeal && (
          <div className="space-y-4">
            {!showAppealForm ? (
              <Button
                onClick={() => setShowAppealForm(true)}
                className="w-full"
                size="lg"
              >
                <AlertTriangle className="h-4 w-4 mr-2" />
                发起申诉
              </Button>
            ) : (
              <div className="space-y-4 p-4 border rounded-lg">
                <h3 className="text-sm font-medium">填写申诉信息</h3>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    申诉理由 *
                  </label>
                  <textarea
                    value={reason}
                    onChange={(e) => setReason(e.target.value)}
                    placeholder="请详细说明申诉的理由..."
                    className="w-full min-h-[100px] p-3 border rounded-md text-sm resize-none"
                    rows={4}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    申诉证据（可选）
                  </label>
                  <textarea
                    value={evidence}
                    onChange={(e) => setEvidence(e.target.value)}
                    placeholder="如果有相关证据或补充说明，请在此处填写..."
                    className="w-full min-h-[80px] p-3 border rounded-md text-sm resize-none"
                    rows={3}
                  />
                </div>

                <div className="flex gap-3">
                  <Button
                    onClick={handleSubmitAppeal}
                    disabled={isSubmitting || !reason.trim()}
                    className="flex-1"
                  >
                    {isSubmitting ? (
                      <>提交中...</>
                    ) : (
                      <>
                        <Send className="h-4 w-4 mr-2" />
                        提交申诉
                      </>
                    )}
                  </Button>
                  
                  <Button
                    onClick={() => {
                      setShowAppealForm(false);
                      setReason('');
                      setEvidence('');
                    }}
                    variant="outline"
                  >
                    取消
                  </Button>
                </div>
              </div>
            )}
          </div>
        )}

        {/* 申诉信息展示 */}
        {isAppealed && appealInfo && (
          <div className="space-y-4">
            <h3 className="text-sm font-medium">申诉详情</h3>
            <div className="space-y-3 p-4 bg-muted/30 rounded-lg">
              <div>
                <span className="text-sm font-medium">申诉理由:</span>
                <p className="text-sm text-muted-foreground mt-1">{appealInfo.reason}</p>
              </div>
              
              {appealInfo.evidence && (
                <div>
                  <span className="text-sm font-medium">申诉证据:</span>
                  <p className="text-sm text-muted-foreground mt-1">{appealInfo.evidence}</p>
                </div>
              )}
              
              <div className="text-xs text-muted-foreground">
                申诉时间: {formatDate(appealInfo.createdAt)}
              </div>
            </div>
          </div>
        )}

        {/* 处理结果展示 */}
        {isProcessed && appealInfo && (
          <div className="space-y-4">
            <h3 className="text-sm font-medium">处理结果</h3>
            <div className="space-y-3 p-4 bg-muted/30 rounded-lg">
              <div>
                <span className="text-sm font-medium">处理决定:</span>
                <Badge className={cn(
                  "ml-2",
                  appealInfo.status === 'approved' 
                    ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
                    : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
                )}>
                  {appealInfo.status === 'approved' ? '同意申诉' : '驳回申诉'}
                </Badge>
              </div>
              
              {appealInfo.finalScore && (
                <div>
                  <span className="text-sm font-medium">调整后分数:</span>
                  <span className={cn("text-lg font-bold ml-2", getScoreColor(appealInfo.finalScore))}>
                    {appealInfo.finalScore}
                  </span>
                </div>
              )}
              
              <div>
                <span className="text-sm font-medium">处理意见:</span>
                <p className="text-sm text-muted-foreground mt-1">{appealInfo.comment}</p>
              </div>
              
              <div className="text-xs text-muted-foreground">
                处理时间: {formatDate(appealInfo.processedAt!)}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}