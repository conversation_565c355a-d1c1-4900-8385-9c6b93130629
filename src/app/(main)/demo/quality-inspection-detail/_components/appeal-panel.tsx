import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { cn } from '@/lib/utils';
import { SessionData, AppealProcessData } from '../types';
import { Shield, MessageSquare, CheckCircle, XCircle } from 'lucide-react';

interface AppealPanelProps {
  session: SessionData;
  onProcessAppeal: (decision: AppealProcessData) => Promise<void>;
}

export function AppealPanel({ session, onProcessAppeal }: AppealPanelProps) {
  const [decision, setDecision] = useState<boolean | null>(null);
  const [finalScore, setFinalScore] = useState(session.finalScore);
  const [comment, setComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const appealInfo = session.appealInfo;
  const originalScore = session.reviewScore || session.machineScore;

  const handleSubmit = async () => {
    if (decision === null || !comment.trim()) return;

    setIsSubmitting(true);
    
    try {
      const processData: AppealProcessData = {
        approved: decision,
        finalScore: decision ? finalScore : originalScore,
        comment: comment.trim()
      };
      
      await onProcessAppeal(processData);
    } catch (error) {
      console.error('Failed to process appeal:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600 dark:text-green-400';
    if (score >= 80) return 'text-blue-600 dark:text-blue-400';
    if (score >= 60) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getDecisionColor = (approved: boolean) => {
    return approved 
      ? 'border-green-200 bg-green-50 dark:border-green-900 dark:bg-green-900/20' 
      : 'border-red-200 bg-red-50 dark:border-red-900 dark:bg-red-900/20';
  };

  if (!appealInfo) {
    return (
      <Card className="bg-gradient-to-br from-background to-muted/20 border-muted-foreground/20">
        <CardContent className="py-8">
          <div className="text-center text-muted-foreground">
            <Shield className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p>暂无申诉信息</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-gradient-to-br from-background to-muted/20 border-muted-foreground/20">
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <Shield className="h-5 w-5" />
          申诉处理
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* 申诉信息 */}
        <div className="space-y-4">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-2">申诉信息</h3>
            <div className="bg-muted/30 rounded-lg p-4 space-y-3">
              <div>
                <span className="text-sm font-medium">申诉人:</span>
                <span className="text-sm ml-2">{session.agent.name}</span>
              </div>
              <div>
                <span className="text-sm font-medium">申诉时间:</span>
                <span className="text-sm ml-2">
                  {new Date(appealInfo.createdAt).toLocaleString('zh-CN')}
                </span>
              </div>
              <div>
                <span className="text-sm font-medium">申诉理由:</span>
                <p className="text-sm mt-1 text-muted-foreground">{appealInfo.reason}</p>
              </div>
              {appealInfo.evidence && (
                <div>
                  <span className="text-sm font-medium">申诉证据:</span>
                  <p className="text-sm mt-1 text-muted-foreground">{appealInfo.evidence}</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 分数对比 */}
        <div className="grid grid-cols-2 gap-4 p-4 bg-muted/50 rounded-lg">
          <div className="text-center">
            <div className="text-2xl font-bold text-muted-foreground">{originalScore}</div>
            <div className="text-sm text-muted-foreground">原分数</div>
          </div>
          <div className="text-center">
            <div className={cn("text-2xl font-bold", getScoreColor(finalScore))}>
              {finalScore}
            </div>
            <div className="text-sm">调整后</div>
          </div>
        </div>

        {/* 处理决定 */}
        <div className="space-y-4">
          <h3 className="text-sm font-medium">处理决定</h3>
          
          <div className="grid grid-cols-2 gap-3">
            <button
              type="button"
              onClick={() => setDecision(true)}
              className={cn(
                "p-4 border-2 rounded-lg text-center transition-all duration-200",
                "hover:scale-105",
                decision === true 
                  ? getDecisionColor(true) 
                  : "border-muted-foreground/20 hover:border-green-300"
              )}
            >
              <CheckCircle className="h-6 w-6 mx-auto mb-2" />
              <div className="text-sm font-medium">同意申诉</div>
            </button>

            <button
              type="button"
              onClick={() => setDecision(false)}
              className={cn(
                "p-4 border-2 rounded-lg text-center transition-all duration-200",
                "hover:scale-105",
                decision === false 
                  ? getDecisionColor(false) 
                  : "border-muted-foreground/20 hover:border-red-300"
              )}
            >
              <XCircle className="h-6 w-6 mx-auto mb-2" />
              <div className="text-sm font-medium">驳回申诉</div>
            </button>
          </div>
        </div>

        {/* 分数调整 */}
        {decision === true && (
          <div className="space-y-2">
            <label className="text-sm font-medium">调整后分数</label>
            <div className="flex items-center gap-2">
              <input
                type="number"
                value={finalScore}
                onChange={(e) => setFinalScore(Math.max(0, Math.min(100, parseInt(e.target.value) || 0)))}
                className="w-20 text-sm border rounded px-2 py-1"
                min="0"
                max="100"
              />
              <span className="text-sm text-muted-foreground">分</span>
            </div>
          </div>
        )}

        {/* 处理意见 */}
        <div className="space-y-2">
          <label className="text-sm font-medium">处理意见</label>
          <Textarea
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            placeholder={
              decision === true 
                ? "请说明同意申诉的理由..."
                : "请说明驳回申诉的理由..."
            }
            rows={4}
            className="resize-none"
          />
        </div>

        {/* 处理结果预览 */}
        {decision !== null && comment && (
          <Alert>
            <MessageSquare className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-1">
                <div>
                  <strong>处理结果:</strong> {decision ? '同意申诉' : '驳回申诉'}
                </div>
                {decision && (
                  <div>
                    <strong>最终分数:</strong> {finalScore}分
                  </div>
                )}
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-3">
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting || decision === null || !comment.trim()}
            className={cn(
              decision === true && "bg-green-600 hover:bg-green-700",
              decision === false && "bg-red-600 hover:bg-red-700"
            )}
          >
            {isSubmitting ? '处理中...' : '提交处理结果'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}