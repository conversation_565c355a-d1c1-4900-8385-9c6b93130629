import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { PageHeaderProps, ViewMode } from '../types';
import { ArrowLeft, Eye, CheckCircle, Shield, Clock } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';

export function PageHeader({ title, subtitle, onBack, mode }: PageHeaderProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const getModeInfo = (mode: ViewMode) => {
    switch (mode) {
      case 'basic_view':
        return {
          icon: Eye,
          label: '基础查看',
          color: 'text-blue-600 bg-blue-100 dark:bg-blue-900/30 dark:text-blue-300'
        };
      case 'review':
        return {
          icon: CheckCircle,
          label: '质检复核',
          color: 'text-purple-600 bg-purple-100 dark:bg-purple-900/30 dark:text-purple-300'
        };
      case 'appeal_processing':
        return {
          icon: Shield,
          label: '申诉处理',
          color: 'text-orange-600 bg-orange-100 dark:bg-orange-900/30 dark:text-orange-300'
        };
      case 'performance_check':
        return {
          icon: Clock,
          label: '绩效检查',
          color: 'text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-300'
        };
      default:
        return {
          icon: Eye,
          label: '查看',
          color: 'text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-300'
        };
    }
  };

  const modeInfo = getModeInfo(mode);
  const ModeIcon = modeInfo.icon;

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      router.back();
    }
  };

  return (
    <div className="flex items-center justify-between p-4 bg-background/80 backdrop-blur-sm border-b">
      <div className="flex items-center space-x-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleBack}
          className="hover:bg-muted transition-colors"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          返回列表
        </Button>
        
        <div className="space-y-1">
          <h1 className="text-xl font-bold flex items-center gap-2">
            {title}
            <Badge className={cn("ml-2 px-2 py-1", modeInfo.color)}>
              <ModeIcon className="h-3 w-3 mr-1" />
              {modeInfo.label}
            </Badge>
          </h1>
          {subtitle && (
            <p className="text-sm text-muted-foreground">
              {subtitle}
            </p>
          )}
        </div>
      </div>

      <div className="flex items-center space-x-2">
        {mode === 'basic_view' && (
          <>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const params = new URLSearchParams(searchParams.toString());
                params.set('viewMode', 'review');
                router.push(`?${params.toString()}`);
              }}
            >
              进入复核模式
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const params = new URLSearchParams(searchParams.toString());
                params.set('viewMode', 'performance_check');
                router.push(`?${params.toString()}`);
              }}
            >
              绩效检查
            </Button>
          </>
        )}
      </div>
    </div>
  );
}