import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { ScorePanelProps } from "../types";

export function ScorePanel({ 
  machineScore, 
  reviewScore, 
  appealScore, 
  finalScore, 
  finalScoreSource,
  isQualified 
}: ScorePanelProps) {
  const getScoreSourceText = (source: string) => {
    switch (source) {
      case 'ai': return 'AI初检';
      case 'review': return '人工复核';
      case 'appeal': return '申诉裁定';
      default: return '未知来源';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600 dark:text-green-400";
    if (score >= 80) return "text-blue-600 dark:text-blue-400";
    if (score >= 60) return "text-yellow-600 dark:text-yellow-400";
    return "text-red-600 dark:text-red-400";
  };

  const getQualifiedColor = () => {
    return isQualified 
      ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300" 
      : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300";
  };

  return (
    <Card className="bg-gradient-to-br from-background to-muted/20 border-muted-foreground/20">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          <span className="text-2xl">🎯</span>
          评分面板
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 最终分数环形图 */}
        <div className="flex flex-col items-center space-y-4">
          <div className="relative w-32 h-32">
            <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 36 36">
              <path
                className="text-muted-foreground/20"
                stroke="currentColor"
                strokeWidth="3"
                fill="none"
                d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
              />
              <path
                className={cn(
                  "transition-all duration-1000 ease-out",
                  finalScore >= 80 ? "text-green-500" : 
                  finalScore >= 60 ? "text-yellow-500" : "text-red-500"
                )}
                stroke="currentColor"
                strokeWidth="3"
                strokeDasharray={`${finalScore}, 100`}
                strokeLinecap="round"
                fill="none"
                d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
              />
            </svg>
            <div className="absolute inset-0 flex flex-col items-center justify-center">
              <span className={cn("text-3xl font-bold", getScoreColor(finalScore))}>
                {finalScore}
              </span>
              <span className="text-sm text-muted-foreground">分</span>
            </div>
          </div>
          
          <Badge className={cn("px-3 py-1", getQualifiedColor())}>
            {isQualified ? '✅ 合格' : '❌ 不合格'}
          </Badge>
        </div>

        {/* 分数演进流程 */}
        <div className="space-y-3">
          <div className="text-sm font-medium text-muted-foreground">分数演进</div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">AI初检</span>
              <span className={getScoreColor(machineScore)}>{machineScore}分</span>
            </div>
            
            {reviewScore && (
              <>
                <div className="h-4 w-px bg-border mx-auto" />
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">人工复核</span>
                  <span className={getScoreColor(reviewScore)}>{reviewScore}分</span>
                </div>
              </>
            )}
            
            {appealScore && (
              <>
                <div className="h-4 w-px bg-border mx-auto" />
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">申诉裁定</span>
                  <span className={getScoreColor(appealScore)}>{appealScore}分</span>
                </div>
              </>
            )}
          </div>
          
          <div className="pt-2 text-xs text-muted-foreground text-center">
            最终分数来源：{getScoreSourceText(finalScoreSource)}
          </div>
        </div>

        {/* 评分标准提示 */}
        <div className="pt-4 border-t border-muted-foreground/10">
          <div className="text-xs text-muted-foreground space-y-1">
            <div>• 合格线：≥ 80分</div>
            <div>• 优秀线：≥ 90分</div>
            <div>• 满分：100分</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Badge 组件
function Badge({ className, children }: { className?: string; children: React.ReactNode }) {
  return (
    <div className={cn(
      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
      className
    )}>
      {children}
    </div>
  );
}