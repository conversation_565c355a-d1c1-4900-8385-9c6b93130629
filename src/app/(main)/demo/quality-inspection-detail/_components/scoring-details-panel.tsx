import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { ScoringDetailsPanelProps, ScoringRule } from "../types";
import { CheckCircle2, XCircle, ArrowRight, Clock } from "lucide-react";

export function ScoringDetailsPanel({ 
  rules, 
  mode, 
  onRuleChange, 
  onSeek 
}: ScoringDetailsPanelProps) {
  const passedRules = rules.filter(rule => rule.isHit);
  const failedRules = rules.filter(rule => !rule.isHit);

  const getRuleStatusIcon = (rule: ScoringRule) => {
    if (rule.isHit) {
      return <CheckCircle2 className="h-4 w-4 text-green-500" />;
    }
    return <XCircle className="h-4 w-4 text-red-500" />;
  };

  const getRuleStatusColor = (rule: ScoringRule) => {
    if (rule.isHit) {
      return "border-green-200 bg-green-50 dark:border-green-900 dark:bg-green-900/20";
    }
    return "border-red-200 bg-red-50 dark:border-red-900 dark:bg-red-900/20";
  };

  const getPointsColor = (points: number) => {
    if (points > 0) return "text-green-600 dark:text-green-400";
    if (points < 0) return "text-red-600 dark:text-red-400";
    return "text-muted-foreground";
  };

  const handleRuleToggle = (ruleId: string) => {
    if (!onRuleChange || mode !== "review") return;

    const rule = rules.find(r => r.id === ruleId);
    if (!rule) return;

    onRuleChange(ruleId, {
      isHit: !rule.isHit,
      reviewedHit: !rule.reviewedHit,
      reviewedPoints: !rule.reviewedHit ? 0 : rule.points
    });
  };

  const handlePointsChange = (ruleId: string, newPoints: number) => {
    if (!onRuleChange || mode !== "review") return;

    onRuleChange(ruleId, {
      reviewedPoints: newPoints,
      isHit: newPoints === 0,
      reviewedHit: newPoints === 0
    });
  };

  const formatTimestamp = (seconds?: number) => {
    if (!seconds && seconds !== 0) return "";
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      '服务规范': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
      '服务态度': 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
      '安全规范': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',
      '业务能力': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
      '沟通技巧': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
    };
    return colors[category] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
  };

  const RuleCard = ({ rule }: { rule: ScoringRule }) => (
    <div className={cn(
      "border rounded-lg p-4 transition-all duration-200",
      getRuleStatusColor(rule),
      mode === "review" && "hover:shadow-md hover:scale-[1.01] cursor-pointer"
    )}
    onClick={() => handleRuleToggle(rule.id)}
    >
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center space-x-2">
          {getRuleStatusIcon(rule)}
          <h3 className="font-medium text-sm">{rule.name}</h3>
          
          <Badge className={getCategoryColor(rule.category)}>
            {rule.category}
          </Badge>
        </div>

        <div className="text-right">
          <span className={cn("font-bold text-lg", getPointsColor(rule.reviewedPoints ?? rule.points))}>
            {rule.reviewedPoints ?? rule.points >= 0 ? '+' : ''}{rule.reviewedPoints ?? rule.points}
          </span>
          <span className="text-xs text-muted-foreground ml-1">分</span>
        </div>
      </div>

      <p className="text-sm text-muted-foreground mb-2">{rule.description}</p>

      <div className="flex items-center justify-between text-xs">
        <div className="flex items-center space-x-2">
          {rule.hitTimestamp && (
            <>
              <Clock className="h-3 w-3 text-muted-foreground" />
              <span className="text-muted-foreground">{formatTimestamp(rule.hitTimestamp)}</span>
              
              {onSeek && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 px-2 text-xs hover:bg-transparent"
                  onClick={(e) => {
                    e.stopPropagation();
                    onSeek(rule.hitTimestamp!);
                  }}
                >
                  <ArrowRight className="h-3 w-3" />
                  跳转
                </Button>
              )}
            </>
          )}
        </div>

        {mode === "review" && rule.reviewedPoints !== undefined && (
          <div className="flex items-center space-x-1">
            <span className="text-xs text-muted-foreground">
              原: {rule.originalHit ? '通过' : '未通过'}
            </span>
            <span className="text-xs">→</span>
            <span className="text-xs font-medium">
              {rule.reviewedHit ? '通过' : '未通过'}
            </span>
          </div>
        )}
      </div>

      {mode === "review" && (
        <div className="mt-3 pt-3 border-t border-border/50">
          <div className="flex items-center space-x-2">
            <span className="text-xs text-muted-foreground">调整后分数:</span>
            <input
              type="number"
              value={rule.reviewedPoints ?? rule.points}
              onChange={(e) => handlePointsChange(rule.id, parseInt(e.target.value) || 0)}
              onClick={(e) => e.stopPropagation()}
              className="w-16 text-xs border rounded px-2 py-1"
              min={-100}
              max={0}
            />
          </div>
        </div>
      )}
    </div>
  );

  return (
    <Card className="bg-gradient-to-br from-background to-muted/20 border-muted-foreground/20">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          <span className="text-2xl">📋</span>
          评分详情
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* 统计信息 */}
        <div className="grid grid-cols-2 gap-4 pb-4 border-b border-muted-foreground/10">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">{passedRules.length}</div>
            <div className="text-sm text-muted-foreground">通过规则</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600 dark:text-red-400">{failedRules.length}</div>
            <div className="text-sm text-muted-foreground">未通过规则</div>
          </div>
        </div>

        {/* 规则列表 */}
        <div className="space-y-3 max-h-[400px] overflow-y-auto pr-2">
          {failedRules.length > 0 && (
            <div>
              <div className="text-sm font-medium text-red-600 dark:text-red-400 mb-2">
                未通过规则 ({failedRules.length})
              </div>
              {failedRules.map(rule => (
                <RuleCard key={rule.id} rule={rule} />
              ))}
            </div>
          )}

          {passedRules.length > 0 && (
            <div>
              <div className="text-sm font-medium text-green-600 dark:text-green-400 mb-2">
                通过规则 ({passedRules.length})
              </div>
              {passedRules.map(rule => (
                <RuleCard key={rule.id} rule={rule} />
              ))}
            </div>
          )}
        </div>

        {/* 复核模式提示 */}
        {mode === "review" && (
          <div className="pt-4 border-t border-muted-foreground/10">
            <div className="text-xs text-muted-foreground bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-lg">
              💡 提示：点击规则卡片可以切换通过/未通过状态，调整分数后系统会自动重新计算总分
            </div>
          </div>
        )}

        {/* 空状态 */}
        {rules.length === 0 && (
          <div className="text-center py-8">
            <div className="text-muted-foreground">暂无评分规则数据</div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}