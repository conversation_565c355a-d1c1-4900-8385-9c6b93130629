import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { SessionData } from '../types';
import { 
  User, 
  Phone, 
  Building2, 
  Clock, 
  Calendar,
  Hash 
} from 'lucide-react';

interface CallInfoPanelProps {
  session: SessionData;
}

export function CallInfoPanel({ session }: CallInfoPanelProps) {
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}分${secs}秒`;
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatPhone = (phone: string) => {
    if (phone.includes('*')) return phone;
    
    // 格式化手机号：138****1234
    if (phone.length === 11) {
      return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3');
    }
    return phone;
  };

  const getQualityResult = (score: number) => {
    if (score >= 90) return { text: '优秀', color: 'text-green-600' };
    if (score >= 80) return { text: '合格', color: 'text-blue-600' };
    if (score >= 60) return { text: '待改进', color: 'text-yellow-600' };
    return { text: '不合格', color: 'text-red-600' };
  };

  const result = getQualityResult(session.finalScore);

  return (
    <Card className="bg-gradient-to-br from-background to-muted/20 border-muted-foreground/20">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          <span className="text-2xl">📱</span>
          通话信息
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 通话基本信息 */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                <User className="h-4 w-4 text-blue-600 dark:text-blue-300" />
              </div>
              <div>
                <div className="text-xs text-muted-foreground">坐席信息</div>
                <div className="font-medium">{session.agent.name}</div>
                <div className="text-xs text-muted-foreground">{session.agent.teamName} - {session.agent.id}</div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                <Building2 className="h-4 w-4 text-green-600 dark:text-green-300" />
              </div>
              <div>
                <div className="text-xs text-muted-foreground">所属班组</div>
                <div className="font-medium">{session.agent.teamName}</div>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                <Phone className="h-4 w-4 text-purple-600 dark:text-purple-300" />
              </div>
              <div>
                <div className="text-xs text-muted-foreground">客户号码</div>
                <div className="font-medium">{formatPhone(session.customer.phone)}</div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
                <Hash className="h-4 w-4 text-orange-600 dark:text-orange-300" />
              </div>
              <div>
                <div className="text-xs text-muted-foreground">客户编号</div>
                <div className="font-medium text-xs">{session.customer.id}</div>
              </div>
            </div>
          </div>
        </div>

        {/* 通话时间信息 */}
        <div className="grid grid-cols-2 gap-4 pt-4 border-t border-muted-foreground/10">
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-cyan-100 dark:bg-cyan-900/30 rounded-lg">
                <Calendar className="h-4 w-4 text-cyan-600 dark:text-cyan-300" />
              </div>
              <div>
                <div className="text-xs text-muted-foreground">通话开始</div>
                <div className="font-medium text-sm">{formatDate(session.startTime)}</div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <div className="p-2 bg-rose-100 dark:bg-rose-900/30 rounded-lg">
                <Clock className="h-4 w-4 text-rose-600 dark:text-rose-300" />
              </div>
              <div>
                <div className="text-xs text-muted-foreground">通话结束</div>
                <div className="font-medium text-sm">{formatDate(session.endTime)}</div>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-indigo-100 dark:bg-indigo-900/30 rounded-lg">
                <Clock className="h-4 w-4 text-indigo-600 dark:text-indigo-300" />
              </div>
              <div>
                <div className="text-xs text-muted-foreground">通话时长</div>
                <div className="font-medium">{formatDuration(session.duration)}</div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <div className="p-2 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg">
                <Hash className="h-4 w-4 text-emerald-600 dark:text-emerald-300" />
              </div>
              <div>
                <div className="text-xs text-muted-foreground">会话编号</div>
                <div className="font-medium text-xs">{session.id}</div>
              </div>
            </div>
          </div>
        </div>

        {/* 质检结果 */}
        <div className="pt-4 border-t border-muted-foreground/10">
          <div className="flex items-center justify-between">
            <div className="text-sm font-medium">质检结果</div>
            
            <div className="flex items-center gap-2">
              <div className={cn("text-2xl font-bold", result.color)}>
                {session.finalScore}
              </div>
              
              <Badge 
                variant="outline" 
                className={cn(
                  "text-sm",
                  result.color,
                  result.color === 'text-green-600' && "border-green-600",
                  result.color === 'text-blue-600' && "border-blue-600",
                  result.color === 'text-yellow-600' && "border-yellow-600",
                  result.color === 'text-red-600' && "border-red-600"
                )}
              >
                {result.text}
              </Badge>
            </div>
          </div>

          <div className="mt-2 text-xs text-muted-foreground">
            质检标准：80分以上为合格，90分以上为优秀
          </div>
        </div>

        {/* 快速统计 */}
        <div className="pt-4 border-t border-muted-foreground/10">
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-blue-600">
                {session.transcript.length}
              </div>
              <div className="text-xs text-muted-foreground">
                对话轮次
              </div>
            </div>
            
            <div>
              <div className="text-2xl font-bold text-purple-600">
                {session.scoringRules.length}
              </div>
              <div className="text-xs text-muted-foreground">
                评分规则
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}