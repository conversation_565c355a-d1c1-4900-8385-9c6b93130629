// 质检会话详情页面的模拟数据

import { SessionData, TranscriptMessage, ScoringRule, ProcessTimelineEvent, ScoreEvolution, AppealInfo } from './types';

// 创建模拟对话文本
const createTranscript = (): TranscriptMessage[] = [
  {
    id: 'msg_1',
    speaker: 'agent',
    text: '您好，这里是XX银行客服，我是坐席王伟，很高兴为您服务。请问有什么可以帮助您的？',
    timestamp: 0,
    duration: 8,
    keywords: ['开场白', '银行', '客服'],
    tags: ['标准开场']
  },
  {
    id: 'msg_2',
    speaker: 'customer',
    text: '你好，我想查询一下我的信用卡账单。',
    timestamp: 10,
    duration: 3,
    keywords: ['信用卡', '账单', '查询']
  },
  {
    id: 'msg_3',
    speaker: 'agent',
    text: '好的，请问您的信用卡卡号是多少？我需要核对一下您的身份信息。',
    timestamp: 14,
    duration: 5,
    keywords: ['信用卡', '卡号', '身份验证']
  },
  {
    id: 'msg_4',
    speaker: 'customer',
    text: '我的卡号是6225开头的，后面是1234 5678。',
    timestamp: 20,
    duration: 4,
    keywords: ['卡号', '6225', '1234', '5678']
  },
  {
    id: 'msg_5',
    speaker: 'agent',
    text: '好的，我为您查询一下。请稍等...',
    timestamp: 25,
    duration: 3,
    tags: ['等待']
  },
  {
    id: 'msg_6',
    speaker: 'agent',
    text: '我这边看到您上个月的账单金额是2,580元，最后还款日是本月15号。请问还有其他需要查询的吗？',
    timestamp: 40,
    duration: 8,
    keywords: ['账单', '2580元', '还款日', '15号']
  },
  {
    id: 'msg_7',
    speaker: 'customer',
    text: '好的，我知道了。谢谢你的帮助。',
    timestamp: 50,
    duration: 3,
    keywords: ['感谢', '帮助']
  },
  {
    id: 'msg_8',
    speaker: 'agent',
    text: '不客气，这是我们应该做的。如果您还有其他问题，随时可以联系我们。祝您生活愉快！',
    timestamp: 55,
    duration: 7,
    tags: ['结束语', '礼貌']
  }
];

// 创建评分规则
const createScoringRules = (): ScoringRule[] => [
  {
    id: 'rule_1',
    name: '标准开场白检测',
    description: '检测坐席是否使用标准开场白，包括问候语、自我介绍和单位介绍',
    category: '服务规范',
    points: 0,
    isHit: true,
    originalHit: true,
    reviewedHit: true,
    reviewedPoints: 0
  },
  {
    id: 'rule_2',
    name: '礼貌用语检测',
    description: '检测坐席是否使用礼貌用语，包括"您好"、"请"、"谢谢"等',
    category: '服务态度',
    points: 0,
    isHit: true,
    originalHit: true,
    reviewedHit: true,
    reviewedPoints: 0
  },
  {
    id: 'rule_3',
    name: '身份验证规范',
    description: '检测坐席是否按照规范进行客户身份验证',
    category: '安全规范',
    points: 0,
    isHit: true,
    originalHit: true,
    reviewedHit: true,
    reviewedPoints: 0
  },
  {
    id: 'rule_4',
    name: '业务解答准确性',
    description: '检测坐席对业务问题的解答是否准确、完整',
    category: '业务能力',
    points: 0,
    isHit: true,
    originalHit: true,
    reviewedHit: true,
    reviewedPoints: 0
  },
  {
    id: 'rule_5',
    name: '结束语规范',
    description: '检测坐席是否使用标准结束语，包括感谢语和祝福语',
    category: '服务规范',
    points: 0,
    isHit: true,
    originalHit: true,
    reviewedHit: true,
    reviewedPoints: 0
  },
  {
    id: 'rule_6',
    name: '语速控制',
    description: '检测坐席语速是否适中，是否过快或过慢',
    category: '沟通技巧',
    points: -5,
    isHit: false,
    originalHit: false,
    reviewedHit: false,
    reviewedPoints: -5,
    hitTimestamp: 30
  },
  {
    id: 'rule_7',
    name: '情绪控制',
    description: '检测坐席是否保持良好的情绪状态',
    category: '服务态度',
    points: 0,
    isHit: true,
    originalHit: true,
    reviewedHit: true,
    reviewedPoints: 0
  }
];

// 创建处理时间轴
const createProcessTimeline = (): ProcessTimelineEvent[] => [
  {
    id: 'event_1',
    type: 'ai_check',
    title: 'AI初检完成',
    description: '系统自动完成初步质检评分',
    timestamp: new Date('2024-01-15T14:30:00'),
    score: 72,
    status: 'completed'
  },
  {
    id: 'event_2',
    type: 'review',
    title: '人工复核',
    description: '质检专员进行人工复核',
    timestamp: new Date('2024-01-15T16:00:00'),
    operator: '李经理',
    score: 80,
    status: 'completed'
  }
];

// 创建分数演进记录
const createScoreEvolution = (): ScoreEvolution[] => [
  {
    source: 'ai',
    score: 72,
    timestamp: new Date('2024-01-15T14:30:00'),
    comment: 'AI系统初次评分'
  },
  {
    source: 'review',
    score: 80,
    timestamp: new Date('2024-01-15T16:00:00'),
    operator: '李经理',
    comment: '人工复核调整分数'
  }
];

// 创建申诉信息
const createAppealInfo = (): AppealInfo => ({
  id: 'appeal_001',
  status: 'pending',
  reason: '我认为AI评分过于严格，我的服务态度和业务能力都得到了客户的认可，希望能够重新评估',
  createdAt: new Date('2024-01-16T09:30:00'),
  evidence: '客户在整个通话过程中都表现得很满意，最后还主动表示感谢'
});

// 创建任务信息
const createTaskInfo = () => ({
  id: 'task_001',
  name: '日常质检任务-20240115',
  status: 'completed' as const,
  progress: 100,
  createdBy: '系统管理员',
  createdAt: new Date('2024-01-15T08:00:00'),
  deadline: new Date('2024-01-15T18:00:00'),
  type: 'scheduled' as const,
  triggerReason: '随机抽样质检'
});

// 创建完整的会话数据
export const mockSessionData: SessionData = {
  id: 'S_A001_20240115_001',
  taskId: 'task_001',
  agent: {
    id: 'A001',
    name: '王伟',
    teamName: 'A组',
    avatar: '/avatars/agent-001.jpg'
  },
  customer: {
    id: 'C_12345',
    phone: '138****1234',
    name: '张女士'
  },
  startTime: new Date('2024-01-15T14:30:05'),
  endTime: new Date('2024-01-15T14:32:20'),
  duration: 135,
  transcript: createTranscript(),
  audioUrl: '/audio/sample-call.mp3',
  
  // 评分相关
  machineScore: 72,
  reviewScore: 80,
  finalScore: 80,
  finalScoreSource: 'review',
  scoringRules: createScoringRules(),
  scoreEvolution: createScoreEvolution(),
  
  // 状态相关
  appealStatus: 'can_appeal',
  appealDeadline: new Date('2024-01-18T18:00:00'),
  
  // 任务相关
  taskInfo: createTaskInfo(),
  
  // 处理记录
  processTimeline: createProcessTimeline()
};

// 不同查看模式的示例数据
export const mockSessionDataWithAppeal: SessionData = {
  ...mockSessionData,
  id: 'S_A001_20240115_002',
  appealStatus: 'appealed',
  appealInfo: createAppealInfo(),
  appealScore: 85,
  finalScore: 85,
  finalScoreSource: 'appeal',
  scoreEvolution: [
    ...createScoreEvolution(),
    {
      source: 'appeal',
      score: 85,
      timestamp: new Date('2024-01-16T10:00:00'),
      operator: '质检主管',
      comment: '申诉处理调整分数'
    }
  ],
  processTimeline: [
    ...createProcessTimeline(),
    {
      id: 'event_3',
      type: 'appeal',
      title: '申诉发起',
      description: '坐席发起申诉申请',
      timestamp: new Date('2024-01-16T09:30:00'),
      operator: '王伟',
      status: 'completed'
    },
    {
      id: 'event_4',
      type: 'appeal',
      title: '申诉处理',
      description: '质检主管处理申诉',
      timestamp: new Date('2024-01-16T10:00:00'),
      operator: '质检主管',
      score: 85,
      status: 'completed'
    }
  ]
};

// 基础查看模式的示例数据
export const mockSessionDataBasic: SessionData = {
  ...mockSessionData,
  id: 'S_A001_20240115_003',
  appealStatus: 'not_applicable'
};

// 获取不同查看模式的示例数据
export function getMockSessionData(mode: string): SessionData {
  switch (mode) {
    case 'review':
      return mockSessionData;
    case 'appeal_processing':
      return mockSessionDataWithAppeal;
    case 'performance_check':
      return mockSessionData;
    case 'basic_view':
    default:
      return mockSessionDataBasic;
  }
}