import { PageHeader } from "./_components/page-header"
import { WorkMetricsSection } from "./_components/work-metrics-section"
import { WorkloadTrendChart } from "./_components/workload-trend-chart"
import { NotificationsSection } from "./_components/notifications-section"
import { PendingTasksList } from "./_components/pending-tasks-list"

/**
 * 复核员仪表板页面
 * 提供复核员的工作台功能
 */
export default function ReviewerDashboardPage() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      <PageHeader />
      <WorkMetricsSection />
      <WorkloadTrendChart />
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <NotificationsSection />
        <PendingTasksList />
      </div>
    </div>
  )
}