"use client"

import { Bell, Info, AlertTriangle, CheckCircle } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

/**
 * 通知数据接口
 */
interface Notification {
  id: string
  title: string
  content: string
  type: "info" | "warning" | "success"
  time: string
  isNew: boolean
}

/**
 * 模拟通知数据
 */
const notifications: Notification[] = [
  {
    id: "1",
    title: "新的复核策略已生效",
    content: "系统已更新复核分配规则，优先分配高风险通话",
    type: "info",
    time: "今天 09:15",
    isNew: true
  },
  {
    id: "2", 
    title: "AI模型已更新",
    content: "质检AI模型已升级到v2.3版本，准确率提升5%",
    type: "success",
    time: "昨天 14:30",
    isNew: true
  },
  {
    id: "3",
    title: "复核积压提醒",
    content: "当前有27个待复核任务，请及时处理",
    type: "warning",
    time: "昨天 10:45",
    isNew: false
  },
  {
    id: "4",
    title: "月度复核报告",
    content: "您的1月份复核工作报告已生成，点击查看",
    type: "info",
    time: "2024-01-20 16:20",
    isNew: false
  }
]

/**
 * 通知公告组件
 * 展示与复核员工作相关的通知
 */
export function NotificationsSection() {
  const getNotificationIcon = (type: Notification["type"]) => {
    switch (type) {
      case "warning":
        return <AlertTriangle className="size-4 text-amber-500" />
      case "success":
        return <CheckCircle className="size-4 text-green-500" />
      default:
        return <Info className="size-4 text-blue-500" />
    }
  }

  const getNotificationColor = (type: Notification["type"]) => {
    switch (type) {
      case "warning":
        return "bg-amber-500/10 text-amber-500 border-amber-500/20"
      case "success":
        return "bg-green-500/10 text-green-500 border-green-500/20"
      default:
        return "bg-blue-500/10 text-blue-500 border-blue-500/20"
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="size-5 text-blue-500" />
          通知公告
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {notifications.map((notification) => (
            <div 
              key={notification.id} 
              className={`p-3 rounded-lg border hover:bg-muted/50 transition-colors ${notification.isNew ? 'bg-muted/30' : ''}`}
            >
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center gap-2">
                  {getNotificationIcon(notification.type)}
                  <h4 className="font-medium text-sm">{notification.title}</h4>
                </div>
                {notification.isNew && (
                  <Badge variant="outline" className="text-xs bg-primary/10 text-primary border-primary/20">
                    新
                  </Badge>
                )}
              </div>
              <p className="text-sm text-muted-foreground mb-2">{notification.content}</p>
              <div className="text-xs text-muted-foreground">{notification.time}</div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}