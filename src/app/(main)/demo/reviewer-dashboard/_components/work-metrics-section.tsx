"use client"

import { TrendingUp, TrendingDown, Minus } from "lucide-react"
import { Card, CardAction, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

/**
 * 工作指标数据接口
 */
interface WorkMetricData {
  title: string
  value: string
  trend: "up" | "down" | "stable"
  trendValue: string
  description: string
  footer: string
}

/**
 * 模拟工作指标数据
 */
const workMetricsData: WorkMetricData[] = [
  { 
    title: "复核总量", 
    value: "342", 
    trend: "up", 
    trendValue: "+12.5%", 
    description: "本周复核量上升",
    footer: "较上周增加38次"
  },
  { 
    title: "待复核量", 
    value: "27", 
    trend: "down", 
    trendValue: "-8.3%", 
    description: "待处理任务减少",
    footer: "工作效率提高"
  },
  { 
    title: "日均复核量", 
    value: "48.9", 
    trend: "stable", 
    trendValue: "+0.2%", 
    description: "工作效率稳定",
    footer: "符合预期目标"
  },
  { 
    title: "AI结果纠错数", 
    value: "86", 
    trend: "up", 
    trendValue: "+15.7%", 
    description: "纠错数量增加",
    footer: "对AI模型优化有价值"
  }
]

/**
 * 核心工作指标组件
 * 展示复核员的关键工作指标
 */
export function WorkMetricsSection() {
  const getTrendIcon = (trend: WorkMetricData["trend"]) => {
    switch (trend) {
      case "up":
        return <TrendingUp className="size-4" />
      case "down":
        return <TrendingDown className="size-4" />
      default:
        return <Minus className="size-4" />
    }
  }

  return (
    <div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs md:grid-cols-2 lg:grid-cols-4">
      {workMetricsData.map((metric, index) => (
        <Card key={index} className="@container/card">
          <CardHeader>
            <CardDescription>{metric.title}</CardDescription>
            <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
              {metric.value}
            </CardTitle>
            <CardAction>
              <Badge variant="outline">
                {getTrendIcon(metric.trend)}
                {metric.trendValue}
              </Badge>
            </CardAction>
          </CardHeader>
          <CardFooter className="flex-col items-start gap-1.5 text-sm">
            <div className="line-clamp-1 flex gap-2 font-medium">
              {metric.description} {getTrendIcon(metric.trend)}
            </div>
            <div className="text-muted-foreground">{metric.footer}</div>
          </CardFooter>
        </Card>
      ))}
    </div>
  )
}

