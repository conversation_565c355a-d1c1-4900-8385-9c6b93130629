"use client"

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"

/**
 * 待复核任务数据接口
 */
interface PendingTask {
  id: string
  agentName: string
  customerPhone: string
  callTime: string
  duration: string
  aiScore: number
  triggerReason: "random" | "lowScore" | "keywords" | "appeal"
  priority: "high" | "medium" | "low"
}

/**
 * 模拟待复核任务数据
 */
const pendingTasks: PendingTask[] = [
  {
    id: "QC20240121005",
    agentName: "张小明",
    customerPhone: "138****1234",
    callTime: "2024-01-21 10:15",
    duration: "5分32秒",
    aiScore: 65.5,
    triggerReason: "lowScore",
    priority: "high"
  },
  {
    id: "QC20240121008", 
    agentName: "李小红",
    customerPhone: "139****5678",
    callTime: "2024-01-21 11:32",
    duration: "8分45秒",
    aiScore: 72.0,
    triggerReason: "keywords",
    priority: "high"
  },
  {
    id: "QC20240121012",
    agentName: "王小强",
    customerPhone: "136****9012",
    callTime: "2024-01-21 13:05",
    duration: "4分18秒",
    aiScore: 78.5,
    triggerReason: "appeal",
    priority: "medium"
  },
  {
    id: "QC20240121015",
    agentName: "赵小丽", 
    customerPhone: "137****3456",
    callTime: "2024-01-21 14:28",
    duration: "6分22秒",
    aiScore: 82.0,
    triggerReason: "random",
    priority: "low"
  },
  {
    id: "QC20240121018",
    agentName: "陈小华", 
    customerPhone: "135****7890",
    callTime: "2024-01-21 15:10",
    duration: "3分47秒",
    aiScore: 68.5,
    triggerReason: "lowScore",
    priority: "medium"
  }
]

/**
 * 待复核任务列表组件
 * 展示需要复核员处理的任务
 */
export function PendingTasksList() {
  const getTriggerReasonText = (reason: PendingTask["triggerReason"]) => {
    switch (reason) {
      case "lowScore":
        return "低分触发"
      case "keywords":
        return "关键词触发"
      case "appeal":
        return "申诉触发"
      case "random":
        return "随机抽检"
    }
  }

  const getTriggerReasonIcon = (reason: PendingTask["triggerReason"]) => {
    switch (reason) {
      case "lowScore":
        return <AlertCircle className="size-3 text-red-500" />
      case "keywords":
        return <AlertCircle className="size-3 text-amber-500" />
      case "appeal":
        return <Clock className="size-3 text-blue-500" />
      case "random":
        return <Clock className="size-3 text-green-500" />
    }
  }

  const getPriorityColor = (priority: PendingTask["priority"]) => {
    switch (priority) {
      case "high":
        return "destructive"
      case "medium":
        return "secondary"
      default:
        return "outline"
    }
  }

  const handleStartReview = (task: PendingTask) => {
    console.log("开始复核任务:", task)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ClipboardList className="size-5 text-primary" />
          待复核任务
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {pendingTasks.map((task) => (
            <div key={task.id} className="p-3 rounded-lg border hover:bg-muted/50 transition-colors">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm">{task.agentName}</span>
                  <Badge variant={getPriorityColor(task.priority)} className="text-xs">
                    AI评分: {task.aiScore}
                  </Badge>
                </div>
                <Button 
                  size="sm"
                  onClick={() => handleStartReview(task)}
                >
                  开始复核
                  <ArrowRight className="ml-1 size-3" />
                </Button>
              </div>
              <div className="text-xs text-muted-foreground space-y-1">
                <div>客户: {task.customerPhone} | 时长: {task.duration}</div>
                <div className="flex items-center gap-1">
                  触发原因: 
                  <span className="flex items-center gap-1">
                    {getTriggerReasonIcon(task.triggerReason)}
                    {getTriggerReasonText(task.triggerReason)}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}