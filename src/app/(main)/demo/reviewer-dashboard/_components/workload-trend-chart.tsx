"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, <PERSON>A<PERSON><PERSON> } from "recharts"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

/**
 * 工作量趋势数据接口
 */
interface WorkloadTrendData {
  date: string
  completedTasks: number
  aiCorrections: number
}

/**
 * 模拟工作量趋势数据
 */
const trendData: WorkloadTrendData[] = [
  { date: "2024-01-08", completedTasks: 45, aiCorrections: 8 },
  { date: "2024-01-09", completedTasks: 52, aiCorrections: 12 },
  { date: "2024-01-10", completedTasks: 48, aiCorrections: 9 },
  { date: "2024-01-11", completedTasks: 56, aiCorrections: 15 },
  { date: "2024-01-12", completedTasks: 61, aiCorrections: 18 },
  { date: "2024-01-13", completedTasks: 49, aiCorrections: 11 },
  { date: "2024-01-14", completedTasks: 58, aiCorrections: 16 },
  { date: "2024-01-15", completedTasks: 53, aiCorrections: 13 },
  { date: "2024-01-16", completedTasks: 59, aiCorrections: 17 },
  { date: "2024-01-17", completedTasks: 64, aiCorrections: 20 },
  { date: "2024-01-18", completedTasks: 57, aiCorrections: 14 },
  { date: "2024-01-19", completedTasks: 62, aiCorrections: 19 },
  { date: "2024-01-20", completedTasks: 55, aiCorrections: 12 },
  { date: "2024-01-21", completedTasks: 51, aiCorrections: 10 }
]

/**
 * 图表配置
 */
const chartConfig = {
  completedTasks: {
    label: "完成复核数",
    color: "var(--chart-1)",
  },
  aiCorrections: {
    label: "AI纠错数",
    color: "var(--chart-2)",
  },
} satisfies ChartConfig

/**
 * 工作量趋势分析图表组件
 * 展示复核员的工作量和AI纠错数量趋势
 */
export function WorkloadTrendChart() {
  return (
    <Card className="@container/card">
      <CardHeader>
        <CardTitle>工作量趋势分析</CardTitle>
      </CardHeader>
      <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
        <ChartContainer config={chartConfig} className="aspect-auto h-[250px] w-full">
          <LineChart data={trendData}>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={(value) => {
                const date = new Date(value);
                return date.toLocaleDateString("zh-CN", {
                  month: "short",
                  day: "numeric",
                });
              }}
            />
            <ChartTooltip
              cursor={false}
              content={
                <ChartTooltipContent
                  labelFormatter={(value) => {
                    return new Date(value).toLocaleDateString("zh-CN", {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    });
                  }}
                  indicator="dot"
                />
              }
            />
            <Line
              dataKey="completedTasks"
              type="monotone"
              stroke="var(--color-completedTasks)"
              strokeWidth={2}
              dot={{
                fill: "var(--color-completedTasks)",
                strokeWidth: 2,
                r: 4,
              }}
              activeDot={{
                r: 6,
              }}
            />
            <Line
              dataKey="aiCorrections"
              type="monotone"
              stroke="var(--color-aiCorrections)"
              strokeWidth={2}
              dot={{
                fill: "var(--color-aiCorrections)",
                strokeWidth: 2,
                r: 4,
              }}
              activeDot={{
                r: 6,
              }}
            />
          </LineChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}
