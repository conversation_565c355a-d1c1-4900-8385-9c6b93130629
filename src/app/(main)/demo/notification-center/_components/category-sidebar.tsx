import { Bell, Clock, CheckCircle, Megaphone } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import type { NotificationCategory } from "../page"

/**
 * 分类导航组件属性接口
 */
interface CategorySidebarProps {
  selectedCategory: string
  onCategoryChange: (category: string) => void
}

/**
 * 模拟分类数据
 */
const categories: NotificationCategory[] = [
  {
    key: "all",
    label: "全部通知",
    count: 45,
    unreadCount: 12
  },
  {
    key: "task-reminder",
    label: "任务提醒",
    count: 18,
    unreadCount: 5
  },
  {
    key: "result-notification",
    label: "结果通知",
    count: 22,
    unreadCount: 4
  },
  {
    key: "system-announcement",
    label: "系统公告",
    count: 5,
    unreadCount: 3
  }
]

/**
 * 左侧分类导航组件
 * 显示通知分类和未读数量
 */
export function CategorySidebar({ selectedCategory, onCategoryChange }: CategorySidebarProps) {
  const getCategoryIcon = (key: string) => {
    switch (key) {
      case "all":
        return <Bell className="size-4" />
      case "task-reminder":
        return <Clock className="size-4" />
      case "result-notification":
        return <CheckCircle className="size-4" />
      case "system-announcement":
        return <Megaphone className="size-4" />
      default:
        return <Bell className="size-4" />
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">通知分类</CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <div className="space-y-1">
          {categories.map((category) => (
            <Button
              key={category.key}
              variant={selectedCategory === category.key ? "secondary" : "ghost"}
              className="w-full justify-start h-auto p-4"
              onClick={() => onCategoryChange(category.key)}
            >
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center gap-3">
                  {getCategoryIcon(category.key)}
                  <span className="font-medium">{category.label}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs">
                    {category.count}
                  </Badge>
                  {category.unreadCount > 0 && (
                    <Badge variant="destructive" className="text-xs">
                      {category.unreadCount}
                    </Badge>
                  )}
                </div>
              </div>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}