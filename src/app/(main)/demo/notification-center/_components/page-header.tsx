import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Trash2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

/**
 * 页面头部组件
 * 显示标题、副标题、未读数量和快捷操作按钮
 */
export function PageHeader() {
  const unreadCount = 12 // 模拟未读数量

  const handleMarkAllRead = () => {
    console.log("标记全部已读")
  }

  const handleClearAll = () => {
    console.log("清空所有通知")
  }

  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-3">
          <div className="flex size-12 items-center justify-center rounded-lg bg-primary/10">
            <Bell className="size-6 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold">通知中心</h1>
            <p className="text-muted-foreground mt-1">查看和管理系统通知消息</p>
          </div>
        </div>
        {unreadCount > 0 && (
          <Badge variant="destructive" className="text-sm">
            {unreadCount}条未读
          </Badge>
        )}
      </div>
      
      <div className="flex items-center gap-3">
        <Button 
          variant="outline" 
          onClick={handleMarkAllRead}
          className="flex items-center gap-2"
        >
          <CheckCheck className="size-4" />
          全部已读
        </Button>
        <Button 
          variant="outline" 
          onClick={handleClearAll}
          className="flex items-center gap-2"
        >
          <Trash2 className="size-4" />
          清空通知
        </Button>
      </div>
    </div>
  )
}