"use client"

import { Search, Calendar, RotateCcw } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"

/**
 * 搜索筛选器组件
 * 提供搜索内容、读取状态、时间范围等筛选功能
 */
export function FilterSection() {
  const handleSearch = () => {
    console.log("执行搜索")
  }

  const handleReset = () => {
    console.log("重置筛选条件")
  }

  return (
    <Card>
      <CardContent className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* 搜索内容 */}
          <div className="space-y-2">
            <Label htmlFor="search">搜索内容</Label>
            <Input
              id="search"
              placeholder="输入关键词搜索"
              className="w-full"
            />
          </div>

          {/* 读取状态 */}
          <div className="space-y-2">
            <Label>读取状态</Label>
            <Select defaultValue="all">
              <SelectTrigger>
                <SelectValue placeholder="选择读取状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部</SelectItem>
                <SelectItem value="read">已读</SelectItem>
                <SelectItem value="unread">未读</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 时间范围 */}
          <div className="space-y-2">
            <Label>时间范围</Label>
            <Button variant="outline" className="w-full justify-start">
              <Calendar className="size-4 mr-2" />
              选择日期范围
            </Button>
          </div>

          {/* 操作按钮 */}
          <div className="space-y-2">
            <Label className="invisible">操作</Label>
            <div className="flex gap-2">
              <Button onClick={handleSearch} className="flex-1">
                <Search className="size-4 mr-2" />
                搜索
              </Button>
              <Button variant="outline" onClick={handleReset}>
                <RotateCcw className="size-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}