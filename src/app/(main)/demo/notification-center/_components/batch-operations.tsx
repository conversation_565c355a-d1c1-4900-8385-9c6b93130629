import { <PERSON>, <PERSON>O<PERSON>, Trash2 } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

/**
 * 批量操作栏组件属性接口
 */
interface BatchOperationsProps {
  selectedNotifications: string[]
  onSelectionChange: (selected: string[]) => void
}

/**
 * 批量操作栏组件
 * 提供全选、批量标记已读/未读、批量删除功能
 */
export function BatchOperations({ selectedNotifications, onSelectionChange }: BatchOperationsProps) {
  const selectedCount = selectedNotifications.length
  const totalCount = 20 // 模拟总数量
  const isAllSelected = selectedCount === totalCount
  const isPartialSelected = selectedCount > 0 && selectedCount < totalCount

  const handleSelectAll = () => {
    if (isAllSelected) {
      onSelectionChange([])
      console.log("取消全选")
    } else {
      // 模拟选择所有通知ID
      const allIds = Array.from({ length: totalCount }, (_, i) => `notification-${i + 1}`)
      onSelectionChange(allIds)
      console.log("全选")
    }
  }

  const handleMarkAsRead = () => {
    console.log("批量标记为已读:", selectedNotifications)
  }

  const handleMarkAsUnread = () => {
    console.log("批量标记为未读:", selectedNotifications)
  }

  const handleDelete = () => {
    console.log("批量删除:", selectedNotifications)
    onSelectionChange([])
  }

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Checkbox
                checked={isAllSelected}
                ref={(el) => {
                  if (el) el.indeterminate = isPartialSelected
                }}
                onCheckedChange={handleSelectAll}
              />
              <span className="text-sm font-medium">
                {isAllSelected ? "取消全选" : "全选"}
              </span>
            </div>
            
            {selectedCount > 0 && (
              <Badge variant="secondary">
                已选择 {selectedCount} 项
              </Badge>
            )}
          </div>

          {selectedCount > 0 && (
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleMarkAsRead}
                className="flex items-center gap-2"
              >
                <Eye className="size-4" />
                标记已读
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleMarkAsUnread}
                className="flex items-center gap-2"
              >
                <EyeOff className="size-4" />
                标记未读
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleDelete}
                className="flex items-center gap-2 text-destructive hover:text-destructive"
              >
                <Trash2 className="size-4" />
                删除
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}