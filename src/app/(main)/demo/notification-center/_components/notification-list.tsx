"use client"

import { formatDistanceToNow } from "date-fns"
import { zhCN } from "date-fns/locale"
import { Clock, CheckCircle, Megaphone, Eye, Trash2 } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import type { Notification } from "../page"

/**
 * 通知列表组件属性接口
 */
interface NotificationListProps {
  selectedCategory: string
  selectedNotifications: string[]
  onSelectionChange: (selected: string[]) => void
}

/**
 * 模拟通知数据
 */
const mockNotifications: Notification[] = [
  {
    id: "1",
    type: "task-reminder",
    title: "质检任务提醒",
    content: "您有5个新的质检任务待处理，请及时完成复核工作。",
    isRead: false,
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2小时前
    priority: "high"
  },
  {
    id: "2",
    type: "result-notification",
    title: "申诉结果通知",
    content: "您的申诉已处理完成，申诉成功，质检分数已更新为85分。",
    isRead: false,
    createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4小时前
    priority: "medium"
  },
  {
    id: "3",
    type: "system-announcement",
    title: "系统维护通知",
    content: "系统将于今晚22:00-24:00进行维护升级，期间可能影响正常使用。",
    isRead: true,
    createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000), // 8小时前
    priority: "high"
  },
  {
    id: "4",
    type: "task-reminder",
    title: "复核任务超时提醒",
    content: "您有2个复核任务即将超时，请尽快处理。",
    isRead: false,
    createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12小时前
    priority: "high"
  },
  {
    id: "5",
    type: "result-notification",
    title: "质检结果通知",
    content: "您的通话记录QC20240121001质检完成，得分92分，质检合格。",
    isRead: true,
    createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1天前
    priority: "low"
  }
]

/**
 * 通知列表组件
 * 显示通知列表，支持选择、标记已读、删除等操作
 */
export function NotificationList({ selectedCategory, selectedNotifications, onSelectionChange }: NotificationListProps) {
  // 根据分类筛选通知
  const filteredNotifications = mockNotifications.filter(notification => {
    if (selectedCategory === "all") return true
    return notification.type === selectedCategory
  })

  const getNotificationIcon = (type: Notification["type"]) => {
    switch (type) {
      case "task-reminder":
        return <Clock className="size-4 text-orange-500" />
      case "result-notification":
        return <CheckCircle className="size-4 text-green-500" />
      case "system-announcement":
        return <Megaphone className="size-4 text-blue-500" />
      default:
        return <Clock className="size-4" />
    }
  }

  const getNotificationTypeBadge = (type: Notification["type"]) => {
    switch (type) {
      case "task-reminder":
        return <Badge variant="outline" className="text-orange-600 border-orange-200">任务提醒</Badge>
      case "result-notification":
        return <Badge variant="outline" className="text-green-600 border-green-200">结果通知</Badge>
      case "system-announcement":
        return <Badge variant="outline" className="text-blue-600 border-blue-200">系统公告</Badge>
      default:
        return <Badge variant="outline">通知</Badge>
    }
  }

  const handleNotificationSelect = (notificationId: string, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedNotifications, notificationId])
    } else {
      onSelectionChange(selectedNotifications.filter(id => id !== notificationId))
    }
  }

  const handleNotificationClick = (notification: Notification) => {
    console.log("点击通知:", notification)
    if (!notification.isRead) {
      console.log("标记为已读:", notification.id)
    }
  }

  const handleMarkAsRead = (notification: Notification, e: React.MouseEvent) => {
    e.stopPropagation()
    console.log("标记为已读:", notification.id)
  }

  const handleDelete = (notification: Notification, e: React.MouseEvent) => {
    e.stopPropagation()
    console.log("删除通知:", notification.id)
  }

  return (
    <Card>
      <CardContent className="p-0">
        <div className="divide-y">
          {filteredNotifications.map((notification) => (
            <div
              key={notification.id}
              className={`p-4 hover:bg-muted/50 transition-colors cursor-pointer ${
                !notification.isRead ? 'bg-muted/30' : ''
              }`}
              onClick={() => handleNotificationClick(notification)}
            >
              <div className="flex items-start gap-3">
                {/* 复选框 */}
                <Checkbox
                  checked={selectedNotifications.includes(notification.id)}
                  onCheckedChange={(checked) => 
                    handleNotificationSelect(notification.id, checked as boolean)
                  }
                  onClick={(e) => e.stopPropagation()}
                />

                {/* 未读状态指示点 */}
                <div className="flex-shrink-0 mt-2">
                  {!notification.isRead && (
                    <div className="size-2 bg-primary rounded-full" />
                  )}
                </div>

                {/* 通知内容 */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {getNotificationIcon(notification.type)}
                      <h4 className={`font-medium text-sm ${!notification.isRead ? 'text-foreground' : 'text-muted-foreground'}`}>
                        {notification.title}
                      </h4>
                      {getNotificationTypeBadge(notification.type)}
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-muted-foreground">
                        {formatDistanceToNow(notification.createdAt, { 
                          addSuffix: true, 
                          locale: zhCN 
                        })}
                      </span>
                      <div className="flex items-center gap-1">
                        {!notification.isRead && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => handleMarkAsRead(notification, e)}
                            className="size-8 p-0"
                          >
                            <Eye className="size-4" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => handleDelete(notification, e)}
                          className="size-8 p-0 text-muted-foreground hover:text-destructive"
                        >
                          <Trash2 className="size-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                  <p className={`text-sm ${!notification.isRead ? 'text-foreground' : 'text-muted-foreground'}`}>
                    {notification.content}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}