"use client"

import { useState } from "react"
import { <PERSON>Header } from "./_components/page-header"
import { CategorySidebar } from "./_components/category-sidebar"
import { FilterSection } from "./_components/filter-section"
import { BatchOperations } from "./_components/batch-operations"
import { NotificationList } from "./_components/notification-list"
import { PaginationSection } from "./_components/pagination-section"

/**
 * 通知数据接口
 */
export interface Notification {
  id: string
  type: "task-reminder" | "result-notification" | "system-announcement"
  title: string
  content: string
  isRead: boolean
  createdAt: Date
  relatedUrl?: string
  priority: "low" | "medium" | "high"
}

/**
 * 通知分类接口
 */
export interface NotificationCategory {
  key: string
  label: string
  count: number
  unreadCount: number
}

/**
 * 通知中心页面
 * 提供完整的多角色通知管理功能
 */
export default function NotificationCenterPage() {
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([])

  return (
    <div className="container mx-auto p-6">
      <PageHeader />
      
      <div className="mt-6 grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* 左侧分类导航 */}
        <div className="lg:col-span-1">
          <CategorySidebar 
            selectedCategory={selectedCategory}
            onCategoryChange={setSelectedCategory}
          />
        </div>
        
        {/* 右侧主内容区域 */}
        <div className="lg:col-span-3 space-y-6">
          <FilterSection />
          <BatchOperations 
            selectedNotifications={selectedNotifications}
            onSelectionChange={setSelectedNotifications}
          />
          <NotificationList 
            selectedCategory={selectedCategory}
            selectedNotifications={selectedNotifications}
            onSelectionChange={setSelectedNotifications}
          />
          <PaginationSection />
        </div>
      </div>
    </div>
  )
}