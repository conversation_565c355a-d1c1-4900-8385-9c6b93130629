import { PageHeader } from "./_components/page-header"
import { PerformanceMetricsSection } from "./_components/performance-metrics-section"
import { ScoreTrendChart } from "./_components/score-trend-chart"
import { ErrorAnalysisSection } from "./_components/error-analysis-section"
import { NotificationsSection } from "./_components/notifications-section"
import { RecentScoresList } from "./_components/recent-scores-list"

/**
 * 客服坐席仪表板页面
 * 提供坐席员的个人工作表现视图
 */
export default function AgentDashboardPage() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      <PageHeader />
      <PerformanceMetricsSection />
      <ScoreTrendChart />
      <ErrorAnalysisSection />
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <NotificationsSection />
        <RecentScoresList />
      </div>
    </div>
  )
}