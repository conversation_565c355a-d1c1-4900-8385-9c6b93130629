"use client"

import { <PERSON>, <PERSON><PERSON>hart, CartesianGrid, <PERSON>A<PERSON><PERSON> } from "recharts"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

/**
 * 成绩趋势数据接口
 */
interface ScoreTrendData {
  date: string
  score: number
}

/**
 * 模拟成绩趋势数据（近14天）
 */
const scoreTrendData: ScoreTrendData[] = [
  { date: "2024-01-08", score: 84.2 },
  { date: "2024-01-09", score: 86.1 },
  { date: "2024-01-10", score: 85.8 },
  { date: "2024-01-11", score: 87.3 },
  { date: "2024-01-12", score: 88.9 },
  { date: "2024-01-13", score: 86.7 },
  { date: "2024-01-14", score: 89.2 },
  { date: "2024-01-15", score: 87.8 },
  { date: "2024-01-16", score: 88.5 },
  { date: "2024-01-17", score: 90.1 },
  { date: "2024-01-18", score: 89.6 },
  { date: "2024-01-19", score: 91.2 },
  { date: "2024-01-20", score: 88.9 },
  { date: "2024-01-21", score: 87.5 }
]

/**
 * 图表配置
 */
const chartConfig = {
  score: {
    label: "质检分数",
    color: "var(--chart-1)",
  },
} satisfies ChartConfig

/**
 * 成绩趋势分析图表组件
 * 展示坐席员近14天的质检分数变化趋势
 */
export function ScoreTrendChart() {
  return (
    <Card className="@container/card">
      <CardHeader>
        <CardTitle>成绩趋势分析</CardTitle>
      </CardHeader>
      <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
        <ChartContainer config={chartConfig} className="aspect-auto h-[250px] w-full">
          <LineChart data={scoreTrendData}>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={(value) => {
                const date = new Date(value);
                return date.toLocaleDateString("zh-CN", {
                  month: "short",
                  day: "numeric",
                });
              }}
            />
            <ChartTooltip
              cursor={false}
              content={
                <ChartTooltipContent
                  labelFormatter={(value) => {
                    return new Date(value).toLocaleDateString("zh-CN", {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    });
                  }}
                  indicator="dot"
                />
              }
            />
            <Line
              dataKey="score"
              type="monotone"
              stroke="var(--color-score)"
              strokeWidth={2}
              dot={{
                fill: "var(--color-score)",
                strokeWidth: 2,
                r: 4,
              }}
              activeDot={{
                r: 6,
              }}
            />
          </LineChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}