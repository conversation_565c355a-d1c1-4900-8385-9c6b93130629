"use client"

import { Bell, Info, AlertTriangle, CheckCircle } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

/**
 * 通知数据接口
 */
interface Notification {
  id: string
  title: string
  content: string
  type: "info" | "warning" | "success"
  time: string
  isNew: boolean
}

/**
 * 模拟通知数据
 */
const notifications: Notification[] = [
  {
    id: "1",
    title: "质检标准更新通知",
    content: "服务质量评分标准已更新，请及时查看新的评分细则",
    type: "info",
    time: "今天 09:30",
    isNew: true
  },
  {
    id: "2",
    title: "月度绩效排名公布",
    content: "恭喜您在本月团队绩效排名中位列第3名！",
    type: "success",
    time: "昨天 16:45",
    isNew: true
  },
  {
    id: "3",
    title: "培训课程提醒",
    content: "客户沟通技巧培训将于明天14:00开始，请准时参加",
    type: "warning",
    time: "昨天 11:20",
    isNew: false
  },
  {
    id: "4",
    title: "申诉结果通知",
    content: "您的质检申诉QC20240120003已处理完成，结果为申诉成功",
    type: "success",
    time: "2024-01-20 14:15",
    isNew: false
  }
]

/**
 * 通知公告组件
 * 展示与坐席相关的通知信息
 */
export function NotificationsSection() {
  const getNotificationIcon = (type: Notification["type"]) => {
    switch (type) {
      case "warning":
        return <AlertTriangle className="size-4 text-amber-500" />
      case "success":
        return <CheckCircle className="size-4 text-green-500" />
      default:
        return <Info className="size-4 text-blue-500" />
    }
  }

  const handleNotificationClick = (notification: Notification) => {
    console.log("点击通知:", notification)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="size-5 text-blue-500" />
          通知公告
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {notifications.map((notification) => (
            <div 
              key={notification.id} 
              className={`p-3 rounded-lg border hover:bg-muted/50 transition-colors cursor-pointer ${notification.isNew ? 'bg-muted/30' : ''}`}
              onClick={() => handleNotificationClick(notification)}
            >
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center gap-2">
                  {getNotificationIcon(notification.type)}
                  <h4 className="font-medium text-sm">{notification.title}</h4>
                </div>
                {notification.isNew && (
                  <Badge variant="outline" className="text-xs bg-primary/10 text-primary border-primary/20">
                    新
                  </Badge>
                )}
              </div>
              <p className="text-sm text-muted-foreground mb-2">{notification.content}</p>
              <div className="text-xs text-muted-foreground">{notification.time}</div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}