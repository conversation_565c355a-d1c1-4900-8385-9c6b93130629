"use client"

import { TrendingUp, TrendingDown, Minus, HelpCircle } from "lucide-react"
import { Card, CardAction, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

/**
 * 绩效指标数据接口
 */
interface PerformanceMetric {
  title: string
  value: string
  trend: "up" | "down" | "stable"
  trendValue: string
  description: string
  footer: string
  helpText: string
}

/**
 * 模拟绩效指标数据
 */
const performanceMetrics: PerformanceMetric[] = [
  {
    title: "平均分数",
    value: "87.5",
    trend: "up",
    trendValue: "+2.3%",
    description: "质检平均分上升",
    footer: "较上周提高2分",
    helpText: "本周期内所有质检通话的平均得分，反映整体服务质量水平"
  },
  {
    title: "团队排名",
    value: "#3",
    trend: "up", 
    trendValue: "↑2",
    description: "排名提升",
    footer: "共12人团队",
    helpText: "在所属团队中的质检成绩排名位置"
  },
  {
    title: "质检合格率",
    value: "92.8%",
    trend: "up",
    trendValue: "+1.5%",
    description: "合格率稳步提升",
    footer: "目标值: 90%",
    helpText: "质检得分达到合格标准的通话占比"
  },
  {
    title: "一次性通过率",
    value: "89.2%",
    trend: "stable",
    trendValue: "+0.1%",
    description: "通过率保持稳定",
    footer: "无需申诉直接通过",
    helpText: "无需申诉即可通过质检的通话占比"
  },
  {
    title: "申诉次数",
    value: "8",
    trend: "down",
    trendValue: "-3",
    description: "申诉次数减少",
    footer: "较上周减少3次",
    helpText: "本周期内发起质检申诉的总次数"
  },
  {
    title: "申诉成功率",
    value: "62.5%",
    trend: "up",
    trendValue: "+12.5%",
    description: "申诉成功率提高",
    footer: "5次成功/8次申诉",
    helpText: "申诉成功次数占总申诉次数的比例"
  }
]

/**
 * 核心绩效指标组件
 * 展示坐席员的6个关键绩效指标
 */
export function PerformanceMetricsSection() {
  const getTrendIcon = (trend: PerformanceMetric["trend"]) => {
    switch (trend) {
      case "up":
        return <TrendingUp className="size-4" />
      case "down":
        return <TrendingDown className="size-4" />
      default:
        return <Minus className="size-4" />
    }
  }

  return (
    <TooltipProvider>
      <div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs md:grid-cols-2 lg:grid-cols-3">
        {performanceMetrics.map((metric, index) => (
          <Card key={index} className="@container/card">
            <CardHeader>
              <CardDescription className="flex items-center gap-2">
                {metric.title}
                <Tooltip>
                  <TooltipTrigger>
                    <HelpCircle className="size-3 text-muted-foreground hover:text-foreground transition-colors" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="max-w-xs">{metric.helpText}</p>
                  </TooltipContent>
                </Tooltip>
              </CardDescription>
              <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
                {metric.value}
              </CardTitle>
              <CardAction>
                <Badge variant="outline">
                  {getTrendIcon(metric.trend)}
                  {metric.trendValue}
                </Badge>
              </CardAction>
            </CardHeader>
            <CardFooter className="flex-col items-start gap-1.5 text-sm">
              <div className="line-clamp-1 flex gap-2 font-medium">
                {metric.description} {getTrendIcon(metric.trend)}
              </div>
              <div className="text-muted-foreground">{metric.footer}</div>
            </CardFooter>
          </Card>
        ))}
      </div>
    </TooltipProvider>
  )
}