"use client"

import { FileText, CheckCircle, XCircle, Clock, MessageSquare } from "lucide-react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

/**
 * 质检记录数据接口
 */
interface QualityRecord {
  id: string
  finalScore: number
  result: "pass" | "fail" | "pending"
  appealStatus: "none" | "pending" | "success" | "failed"
  customerPhone: string
  callTime: string
  duration: string
  callType: string
}

/**
 * 模拟近期成绩数据
 */
const recentScores: QualityRecord[] = [
  {
    id: "QC20240121012",
    finalScore: 92.5,
    result: "pass",
    appealStatus: "none",
    customerPhone: "138****1234",
    callTime: "2024-01-21 14:30",
    duration: "6分32秒",
    callType: "咨询"
  },
  {
    id: "QC20240121008",
    finalScore: 78.0,
    result: "fail",
    appealStatus: "success",
    customerPhone: "139****5678",
    callTime: "2024-01-21 11:15",
    duration: "8分45秒",
    callType: "投诉"
  },
  {
    id: "QC20240120015",
    finalScore: 85.5,
    result: "pass",
    appealStatus: "none",
    customerPhone: "136****9012",
    callTime: "2024-01-20 16:20",
    duration: "4分18秒",
    callType: "业务办理"
  },
  {
    id: "QC20240120011",
    finalScore: 72.0,
    result: "fail",
    appealStatus: "pending",
    customerPhone: "137****3456",
    callTime: "2024-01-20 13:45",
    duration: "7分22秒",
    callType: "咨询"
  },
  {
    id: "QC20240119018",
    finalScore: 89.0,
    result: "pass",
    appealStatus: "none",
    customerPhone: "135****7890",
    callTime: "2024-01-19 15:10",
    duration: "5分47秒",
    callType: "回访"
  },
  {
    id: "QC20240119014",
    finalScore: 76.5,
    result: "fail",
    appealStatus: "failed",
    customerPhone: "134****2468",
    callTime: "2024-01-19 10:30",
    duration: "9分15秒",
    callType: "投诉"
  }
]

/**
 * 近期成绩列表组件
 * 展示坐席员最近的质检记录
 */
export function RecentScoresList() {
  const getResultIcon = (result: QualityRecord["result"]) => {
    switch (result) {
      case "pass":
        return <CheckCircle className="size-4 text-green-500" />
      case "fail":
        return <XCircle className="size-4 text-red-500" />
      default:
        return <Clock className="size-4 text-amber-500" />
    }
  }

  const getResultText = (result: QualityRecord["result"]) => {
    switch (result) {
      case "pass":
        return "合格"
      case "fail":
        return "不合格"
      default:
        return "待定"
    }
  }

  const getResultColor = (result: QualityRecord["result"]) => {
    switch (result) {
      case "pass":
        return "bg-green-500/10 text-green-500 border-green-500/20"
      case "fail":
        return "bg-red-500/10 text-red-500 border-red-500/20"
      default:
        return "bg-amber-500/10 text-amber-500 border-amber-500/20"
    }
  }

  const getAppealStatusText = (status: QualityRecord["appealStatus"]) => {
    switch (status) {
      case "pending":
        return "申诉中"
      case "success":
        return "申诉成功"
      case "failed":
        return "申诉失败"
      default:
        return ""
    }
  }

  const getAppealStatusColor = (status: QualityRecord["appealStatus"]) => {
    switch (status) {
      case "pending":
        return "bg-blue-500/10 text-blue-500 border-blue-500/20"
      case "success":
        return "bg-green-500/10 text-green-500 border-green-500/20"
      case "failed":
        return "bg-red-500/10 text-red-500 border-red-500/20"
      default:
        return ""
    }
  }

  const handleRecordClick = (record: QualityRecord) => {
    console.log("查看质检详情:", record)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="size-5 text-primary" />
          近期成绩
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {recentScores.map((record) => (
            <div 
              key={record.id} 
              className="p-3 rounded-lg border hover:bg-muted/50 transition-colors cursor-pointer"
              onClick={() => handleRecordClick(record)}
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm">{record.id}</span>
                  <Badge variant="outline" className={getResultColor(record.result)}>
                    {getResultIcon(record.result)}
                    {getResultText(record.result)}
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-semibold text-lg">{record.finalScore}</span>
                  {record.appealStatus !== "none" && (
                    <Badge variant="outline" className={getAppealStatusColor(record.appealStatus)}>
                      <MessageSquare className="size-3 mr-1" />
                      {getAppealStatusText(record.appealStatus)}
                    </Badge>
                  )}
                </div>
              </div>
              <div className="text-xs text-muted-foreground space-y-1">
                <div>客户: {record.customerPhone} | 类型: {record.callType}</div>
                <div>时间: {record.callTime} | 时长: {record.duration}</div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}