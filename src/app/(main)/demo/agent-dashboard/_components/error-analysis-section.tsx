"use client"

import { Al<PERSON><PERSON>riangle, TrendingDown } from "lucide-react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

/**
 * 严重错误项数据接口
 */
interface SeriousError {
  id: string
  errorType: string
  count: number
  impact: number
  trend: "up" | "down" | "stable"
}

/**
 * 高频失分项数据接口
 */
interface FrequentDeduction {
  id: string
  deductionItem: string
  count: number
  totalDeduction: number
  trend: "up" | "down" | "stable"
}

/**
 * 模拟严重错误项数据
 */
const seriousErrors: SeriousError[] = [
  { id: "1", errorType: "服务态度不佳", count: 3, impact: -15, trend: "down" },
  { id: "2", errorType: "未按流程操作", count: 2, impact: -10, trend: "stable" },
  { id: "3", errorType: "信息泄露风险", count: 1, impact: -20, trend: "down" },
  { id: "4", errorType: "违规承诺", count: 2, impact: -12, trend: "up" },
  { id: "5", errorType: "挂断客户电话", count: 1, impact: -25, trend: "stable" }
]

/**
 * 模拟高频失分项数据
 */
const frequentDeductions: FrequentDeduction[] = [
  { id: "1", deductionItem: "开场白不规范", count: 8, totalDeduction: -16, trend: "down" },
  { id: "2", deductionItem: "未确认客户信息", count: 6, totalDeduction: -12, trend: "stable" },
  { id: "3", deductionItem: "结束语不完整", count: 5, totalDeduction: -10, trend: "up" },
  { id: "4", deductionItem: "语速过快", count: 4, totalDeduction: -8, trend: "down" },
  { id: "5", deductionItem: "专业术语使用错误", count: 3, totalDeduction: -9, trend: "stable" }
]

/**
 * 错误分析统计组件
 * 展示坐席员的错误分析数据
 */
export function ErrorAnalysisSection() {
  const getTrendColor = (trend: "up" | "down" | "stable") => {
    switch (trend) {
      case "up":
        return "text-red-500"
      case "down":
        return "text-green-500"
      default:
        return "text-muted-foreground"
    }
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* 严重错误项 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="size-5 text-red-500" />
            严重错误项 Top5
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {seriousErrors.map((error, index) => (
              <div key={error.id} className="flex items-center justify-between p-3 rounded-lg border">
                <div className="flex items-center gap-3">
                  <Badge variant="outline" className="bg-red-500/10 text-red-500 border-red-500/20">
                    #{index + 1}
                  </Badge>
                  <div>
                    <div className="font-medium text-sm">{error.errorType}</div>
                    <div className="text-xs text-muted-foreground">
                      触犯 {error.count} 次 | 影响分数 {error.impact}
                    </div>
                  </div>
                </div>
                <TrendingDown className={`size-4 ${getTrendColor(error.trend)}`} />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 高频失分项 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingDown className="size-5 text-amber-500" />
            高频失分项 Top5
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {frequentDeductions.map((deduction, index) => (
              <div key={deduction.id} className="flex items-center justify-between p-3 rounded-lg border">
                <div className="flex items-center gap-3">
                  <Badge variant="outline" className="bg-amber-500/10 text-amber-500 border-amber-500/20">
                    #{index + 1}
                  </Badge>
                  <div>
                    <div className="font-medium text-sm">{deduction.deductionItem}</div>
                    <div className="text-xs text-muted-foreground">
                      失分 {deduction.count} 次 | 累计扣分 {deduction.totalDeduction}
                    </div>
                  </div>
                </div>
                <TrendingDown className={`size-4 ${getTrendColor(deduction.trend)}`} />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}