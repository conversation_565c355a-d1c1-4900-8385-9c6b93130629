"use client"

import { User } from "lucide-react"
import { Badge } from "@/components/ui/badge"

/**
 * 页面头部组件
 * 展示坐席员的基本信息和状态
 */
export function PageHeader() {
  return (
    <div className="mb-8">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <User className="size-8 text-primary" />
            <div>
              <h1 className="text-3xl font-bold">坐席员视角</h1>
              <p className="text-muted-foreground mt-2">
                欢迎回来，张小明 | 客服部-A组
              </p>
            </div>
          </div>
        </div>
        <Badge variant="outline" className="text-lg px-4 py-2 bg-green-500/10 text-green-500 border-green-500/20">
          在线
        </Badge>
      </div>
    </div>
  )
}