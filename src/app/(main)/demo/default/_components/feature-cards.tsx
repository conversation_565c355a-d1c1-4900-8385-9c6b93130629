import {
  AlertTriangle,
  BarChart3,
  Bot,
  Clock,
  Shield,
  Target,
  Users,
  Zap
} from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

const features = [
  {
    icon: Bot,
    title: "AI引擎全量质检",
    description: "100%全量覆盖，彻底告别低效抽检模式，确保每一个客户交互都被纳入监控范围",
    badge: "核心功能",
    color: "text-blue-500"
  },
  {
    icon: AlertTriangle,
    title: "实时风险监控",
    description: "实时预警中心构建服务作战指挥室，变事后补救为事中干预",
    badge: "实时监控",
    color: "text-orange-500"
  },
  {
    icon: BarChart3,
    title: "数据驱动决策",
    description: "全维度数据分析，为管理决策提供科学依据，持续优化服务质量",
    badge: "智能分析",
    color: "text-green-500"
  },
  {
    icon: Users,
    title: "多角色协同",
    description: "为坐席、班组长、复核员、质检主管提供专属工作台，实现高效协同",
    badge: "协同管理",
    color: "text-purple-500"
  },
  {
    icon: Clock,
    title: "准实时反馈",
    description: "将原来数天的工作量压缩至分钟级别，实现准实时的质检结果反馈",
    badge: "高效处理",
    color: "text-cyan-500"
  },
  {
    icon: Shield,
    title: "统一质检标准",
    description: "用统一、客观的规则取代人工主观判断，确保评分公平公正",
    badge: "标准化",
    color: "text-indigo-500"
  }
];

export function FeatureCards() {
  return (
    <section className="relative py-20 md:py-32 overflow-hidden bg-gradient-to-b from-transparent via-muted/20 to-transparent">
      <div className="container mx-auto px-4">
        <div className="mx-auto mb-16 max-w-3xl text-center">
          <h2 className="mb-4 text-3xl font-bold tracking-tight md:text-4xl animate-in fade-in slide-in-from-bottom-3 duration-500">
            四大核心支柱重塑质检范式
          </h2>
          <p className="text-lg text-muted-foreground animate-in fade-in slide-in-from-bottom-4 duration-500">
            通过AI驱动、数据赋能，构建全流程、闭环式服务质量管理的综合性平台
          </p>
        </div>
        
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {features.map((feature, index) => (
            <Card 
              key={index} 
              className="group relative overflow-hidden transition-all duration-500 hover:shadow-xl hover:shadow-primary/10 hover:-translate-y-2"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5 opacity-0 transition-opacity duration-500 group-hover:opacity-100"></div>
              
              <div className="absolute -right-4 -top-4 h-24 w-24 rounded-full bg-primary/10 blur-2xl transition-all duration-500 group-hover:h-32 group-hover:w-32 group-hover:bg-primary/20"></div>
              <CardHeader className="relative pb-4">
                <div className="flex items-center justify-between">
                  <div className={`rounded-xl bg-gradient-to-br from-muted to-muted/80 p-3 shadow-sm transition-all duration-500 group-hover:scale-110 group-hover:shadow-md ${feature.color}`}>
                    <feature.icon className="h-6 w-6 transition-transform duration-500 group-hover:rotate-12" />
                  </div>
                  <Badge 
                    variant="secondary" 
                    className="text-xs transition-all duration-500 group-hover:bg-primary group-hover:text-primary-foreground"
                  >
                    {feature.badge}
                  </Badge>
                </div>
                <CardTitle className="text-xl transition-colors duration-500 group-hover:text-primary">
                  {feature.title}
                </CardTitle>
              </CardHeader>
              
              <CardContent className="relative">
                <CardDescription className="text-sm leading-relaxed transition-all duration-500 group-hover:text-foreground/80">
                  {feature.description}
                </CardDescription>
              </CardContent>
              
              {/* 悬停边框效果 */}
              <div className="absolute inset-0 rounded-lg border-2 border-transparent transition-all duration-500 group-hover:border-primary/20"></div>
            </Card>
          ))}
        </div>
      </div>
      
      {/* 背景装饰 */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute left-1/3 top-1/3 h-48 w-48 rounded-full bg-primary/5 blur-2xl animate-pulse"></div>
        <div className="absolute right-1/3 bottom-1/3 h-40 w-40 rounded-full bg-secondary/10 blur-2xl animate-pulse [animation-delay:2000ms]"></div>
      </div>
    </section>
  );
}