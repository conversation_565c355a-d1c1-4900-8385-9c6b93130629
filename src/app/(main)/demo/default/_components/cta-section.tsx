import { <PERSON><PERSON><PERSON>, <PERSON>rk<PERSON>, <PERSON>, Zap } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

export function CTASection() {
  return (
    <section className="relative py-20 md:py-32 overflow-hidden">
      <div className="container mx-auto px-4">
        <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-primary/10 via-background to-secondary/10">
          <CardContent className="relative p-12 md:p-16">
            <div className="mx-auto max-w-3xl text-center">
              <Badge 
                variant="secondary" 
                className="mb-6 px-4 py-2 animate-in fade-in slide-in-from-bottom-2 duration-500"
              >
                <Sparkles className="mr-2 h-4 w-4 animate-pulse" />
                开启智能质检新时代
              </Badge>
              
              {/* 主标题 */}
              <h2 className="mb-6 text-3xl font-bold tracking-tight md:text-4xl lg:text-5xl animate-in fade-in slide-in-from-bottom-3 duration-500">
                准备好迈入
                <span className="bg-gradient-to-r from-primary via-primary/80 to-primary/60 bg-clip-text text-transparent animate-gradient bg-[length:200%_auto]">
                  智能质检时代
                </span>
                了吗？
              </h2>
              
              {/* 描述 */}
              <p className="mb-8 text-lg text-muted-foreground md:text-xl animate-in fade-in slide-in-from-bottom-4 duration-500">
                从传统的人工抽检到AI驱动的全量质检，从被动分析到主动干预，
                让我们一起构建更高效、更公平、更智能的客服质检体系
              </p>
              
              {/* 特性亮点 */}
              <div className="mb-10 flex flex-wrap justify-center gap-6 text-sm">
                <div className="flex items-center animate-in fade-in slide-in-from-bottom-5 duration-500">
                  <div className="relative">
                    <Zap className="mr-2 h-4 w-4 text-primary animate-pulse" />
                    <div className="absolute inset-0 h-4 w-4 animate-ping rounded-full bg-primary/20"></div>
                  </div>
                  <span className="text-muted-foreground transition-colors duration-300">快速部署，即刻生效</span>
                </div>
                
                <div className="flex items-center animate-in fade-in slide-in-from-bottom-5 duration-500 [animation-delay:200ms]">
                  <Users className="mr-2 h-4 w-4 text-primary" />
                  <span className="text-muted-foreground transition-colors duration-300">专业团队，全程支持</span>
                </div>
                
                <div className="flex items-center animate-in fade-in slide-in-from-bottom-5 duration-500 [animation-delay:400ms]">
                  <Sparkles className="mr-2 h-4 w-4 text-primary" />
                  <span className="text-muted-foreground transition-colors duration-300">持续优化，不断进化</span>
                </div>
              </div>
              
              {/* 行动按钮 */}
              <div className="flex flex-col gap-4 sm:flex-row sm:justify-center animate-in fade-in slide-in-from-bottom-6 duration-500">
                <Button 
                  size="lg" 
                  className="group relative overflow-hidden px-8 py-3 text-base font-medium transition-all duration-300 hover:scale-105"
                >
                  <span className="relative z-10">立即开始体验</span>
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                  <div className="absolute inset-0 -z-10 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full transition-transform duration-700 group-hover:translate-x-full"></div>
                </Button>
                
                <Button 
                  variant="outline" 
                  size="lg" 
                  className="group px-8 py-3 text-base font-medium transition-all duration-300 hover:scale-105 hover:bg-primary hover:text-primary-foreground"
                >
                  预约产品演示
                </Button>
              </div>
              
              {/* 补充信息 */}
              <p className="mt-8 text-xs text-muted-foreground animate-in fade-in slide-in-from-bottom-7 duration-500">
                支持私有化部署 • 7×24小时技术支持 • 30天免费试用
              </p>
            </div>
          </CardContent>
          
          {/* 背景装饰 */}
          <div className="absolute inset-0 -z-10 overflow-hidden">
            <div className="absolute right-1/4 top-1/4 h-32 w-32 rounded-full bg-primary/10 blur-2xl animate-pulse"></div>
            <div className="absolute left-1/4 bottom-1/4 h-24 w-24 rounded-full bg-secondary/20 blur-xl animate-pulse [animation-delay:1000ms]"></div>
            
            {/* 渐变背景 */}
            <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-primary/5 via-transparent to-transparent"></div>
            <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom,_var(--tw-gradient-stops))] from-secondary/5 via-transparent to-transparent"></div>
          </div>
          
          {/* 边框装饰 */}
          <div className="absolute inset-0 rounded-lg border border-primary/20"></div>
          <div className="absolute inset-0 rounded-lg bg-gradient-to-br from-transparent via-primary/5 to-transparent"></div>
        </Card>
      </div>
      
      {/* 背景装饰 */}
      <div className="absolute inset-0 -z-20 overflow-hidden">
        <div className="absolute left-1/2 top-1/2 h-96 w-96 -translate-x-1/2 -translate-y-1/2 rounded-full bg-primary/5 blur-3xl animate-pulse"></div>
      </div>
    </section>
  );
}