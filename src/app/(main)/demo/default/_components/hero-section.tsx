import { ArrowR<PERSON>, Shield, Users, Zap } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";

export function HeroSection() {
  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-background via-background to-muted/20 py-20 md:py-32">
      <div className="container mx-auto px-4">
        <div className="mx-auto max-w-4xl text-center">
          {/* 产品标识 */}
          <Badge 
            variant="secondary" 
            className="mb-6 px-4 py-2 text-sm font-medium animate-in fade-in slide-in-from-bottom-3 duration-500"
          >
            <Shield className="mr-2 h-4 w-4" />
            AI 驱动的质检解决方案
          </Badge>
          
          {/* 主标题 */}
          <h1 className="mb-6 text-4xl font-bold tracking-tight text-foreground md:text-6xl lg:text-7xl animate-in fade-in slide-in-from-bottom-4 duration-700">
            智能客服
            <span className="bg-gradient-to-r from-primary via-primary/80 to-primary/60 bg-clip-text text-transparent animate-gradient bg-[length:200%_auto]">
              质检系统
            </span>
          </h1>
          
          {/* 副标题 */}
          <p className="mb-8 text-lg text-muted-foreground md:text-xl lg:text-2xl animate-in fade-in slide-in-from-bottom-5 duration-700">
            以AI引擎实现100%全量自动化质检，构建全流程、闭环式服务质量管理平台
          </p>
          
          {/* 核心价值点 */}
          <div className="mb-10 flex flex-wrap justify-center gap-6 text-sm text-muted-foreground">
            <div className="flex items-center animate-in fade-in slide-in-from-bottom-6 duration-700">
              <div className="relative">
                <Zap className="mr-2 h-4 w-4 text-primary animate-pulse" />
                <div className="absolute inset-0 h-4 w-4 animate-ping rounded-full bg-primary/20"></div>
              </div>
              实时监控与干预
            </div>
            <div className="flex items-center animate-in fade-in slide-in-from-bottom-6 duration-700 [animation-delay:100ms]">
              <Shield className="mr-2 h-4 w-4 text-primary" />
              客观统一的质检标准
            </div>
            <div className="flex items-center animate-in fade-in slide-in-from-bottom-6 duration-700 [animation-delay:200ms]">
              <Users className="mr-2 h-4 w-4 text-primary" />
              多角色协同管理
            </div>
          </div>
          
          {/* 行动按钮 */}
          <div className="flex flex-col gap-4 sm:flex-row sm:justify-center animate-in fade-in slide-in-from-bottom-7 duration-700 [animation-delay:300ms]">
            <Button 
              size="lg" 
              className="group relative overflow-hidden px-8 py-3 text-base transition-all duration-300 hover:scale-105"
            >
              <span className="relative z-10">立即体验</span>
              <ArrowRight className="ml-2 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
              <div className="absolute inset-0 -z-10 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full transition-transform duration-700 group-hover:translate-x-full"></div>
            </Button>
            <Button 
              variant="outline" 
              size="lg" 
              className="group px-8 py-3 text-base transition-all duration-300 hover:scale-105 hover:bg-primary hover:text-primary-foreground"
            >
              了解更多
            </Button>
          </div>
        </div>
      </div>
      
      {/* 背景装饰 */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute left-1/2 top-1/2 h-96 w-96 -translate-x-1/2 -translate-y-1/2 rounded-full bg-primary/5 blur-3xl animate-pulse"></div>
        <div className="absolute right-1/4 top-1/4 h-64 w-64 rounded-full bg-secondary/10 blur-2xl animate-pulse [animation-delay:1000ms]"></div>
        <div className="absolute bottom-1/4 left-1/4 h-48 w-48 rounded-full bg-primary/5 blur-xl animate-bounce [animation-duration:4s]"></div>
      </div>
      
      {/* 网格背景 */}
      <div className="absolute inset-0 -z-20 opacity-10">
        <div className="h-full w-full bg-[linear-gradient(to_right,#8882_1px,transparent_1px),linear-gradient(to_bottom,#8882_1px,transparent_1px)] bg-[size:24px_24px]"></div>
      </div>
    </section>
  );
}