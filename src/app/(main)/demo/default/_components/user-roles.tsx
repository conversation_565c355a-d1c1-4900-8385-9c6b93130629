import {
  ArrowRight,
  ClipboardCheck,
  Eye,
  Shield,
  UserCheck
} from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

const roles = [
  {
    icon: Eye,
    title: "客服坐席",
    subtitle: "透明的绩效镜子与成长助手",
    description: "通过个人首页清晰看到质检得分、排名和具体扣分项，提供正式申诉流程，提升质检透明度和公平性",
    features: ["个人质检成绩", "通话录音回放", "申诉流程", "成长建议"],
    color: "border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950/20",
    iconColor: "text-blue-600 dark:text-blue-400"
  },
  {
    icon: UserCheck,
    title: "班组长",
    subtitle: "精准的团队雷达与辅导工具",
    description: "工作台汇聚团队宏观表现和个体详细数据，快速识别团队短板，发现明星员工，实现精准管理和有效辅导",
    features: ["团队质检概览", "成员表现分析", "辅导建议", "绩效排名"],
    color: "border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950/20",
    iconColor: "text-green-600 dark:text-green-400"
  },
  {
    icon: ClipboardCheck,
    title: "复核员",
    subtitle: "高效的任务处理器与AI校准器",
    description: "清晰高效的复核任务工作台，便捷完成听音、核对和修正AI评分，每次纠错都是优化AI模型的宝贵输入",
    features: ["复核任务管理", "多模式会话详情", "AI评分修正", "质量校准"],
    color: "border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950/20",
    iconColor: "text-orange-600 dark:text-orange-400"
  },
  {
    icon: Shield,
    title: "质检主管",
    subtitle: "强大的战略驾驶舱与体系设计器",
    description: "拥有系统最高权限，从全局视角监控服务质量，设计词库、规则、方案和策略，是整个游戏规则的设计者",
    features: ["全局质检监控", "规则体系设计", "申诉最终仲裁", "战略决策支持"],
    color: "border-purple-200 bg-purple-50 dark:border-purple-800 dark:bg-purple-950/20",
    iconColor: "text-purple-600 dark:text-purple-400"
  }
];

export function UserRoles() {
  return (
    <section className="relative bg-gradient-to-b from-muted/20 via-background to-muted/20 py-20 md:py-32 overflow-hidden">
      <div className="container mx-auto px-4">
        <div className="mx-auto mb-16 max-w-3xl text-center">
          <h2 className="mb-4 text-3xl font-bold tracking-tight md:text-4xl animate-in fade-in slide-in-from-bottom-3 duration-500">
            为每个角色量身定制的工作体验
          </h2>
          <p className="text-lg text-muted-foreground animate-in fade-in slide-in-from-bottom-4 duration-500">
            从坐席到主管，每个角色都有专属的工作台和功能模块，实现高效协同管理
          </p>
        </div>
        
        <div className="grid gap-8 md:grid-cols-2">
          {roles.map((role, index) => (
            <Card 
              key={index} 
              className={`group relative overflow-hidden transition-all duration-500 hover:shadow-xl hover:-translate-y-2 ${role.color}`}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-background/50 via-transparent to-background/50 opacity-0 transition-opacity duration-500 group-hover:opacity-100"></div>
              
              <div className="absolute -right-8 -top-8 h-32 w-32 rounded-full bg-primary/10 blur-3xl transition-all duration-500 group-hover:h-40 group-hover:w-40 group-hover:bg-primary/20"></div>
              
              <CardHeader className="relative pb-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-4">
                    <div className={`rounded-xl bg-gradient-to-br from-background to-background/80 p-3 shadow-lg transition-all duration-500 group-hover:scale-110 group-hover:shadow-xl ${role.iconColor}`}>
                      <role.icon className="h-6 w-6 transition-transform duration-500 group-hover:rotate-12" />
                    </div>
                    <div>
                      <CardTitle className="text-xl transition-colors duration-500 group-hover:text-primary">{role.title}</CardTitle>
                      <p className="text-sm font-medium text-muted-foreground transition-colors duration-500 group-hover:text-foreground/80">
                        {role.subtitle}
                      </p>
                    </div>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="relative space-y-4">
                <CardDescription className="leading-relaxed transition-all duration-500 group-hover:text-foreground/90">
                  {role.description}
                </CardDescription>
                
                <div className="space-y-3">
                  <p className="text-sm font-medium transition-colors duration-500 group-hover:text-primary">核心功能：</p>
                  <div className="flex flex-wrap gap-2">
                    {role.features.map((feature, featureIndex) => (
                      <Badge 
                        key={featureIndex} 
                        variant="secondary" 
                        className="text-xs transition-all duration-500 group-hover:bg-primary/10 group-hover:text-primary"
                      >
                        {feature}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="group/btn relative overflow-hidden p-0 h-auto font-medium transition-all duration-500 hover:pl-2"
                >
                  <span className="relative z-10 transition-colors duration-500">了解更多</span>
                  <ArrowRight className="ml-2 h-3 w-3 transition-all duration-500 group-hover/btn:translate-x-1" />
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-primary/10 to-transparent -translate-x-full transition-transform duration-700 group-hover/btn:translate-x-full"></div>
                </Button>
              </CardContent>
              
              {/* 悬停边框效果 */}
              <div className="absolute inset-0 rounded-lg border-2 border-transparent transition-all duration-500 group-hover:border-primary/20"></div>
            </Card>
          ))}
        </div>
      </div>
      
      {/* 背景装饰 */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute left-1/4 bottom-1/4 h-64 w-64 rounded-full bg-primary/5 blur-3xl animate-pulse"></div>
        <div className="absolute right-1/4 top-1/4 h-56 w-56 rounded-full bg-secondary/10 blur-3xl animate-pulse [animation-delay:1000ms]"></div>
      </div>
    </section>
  );
}