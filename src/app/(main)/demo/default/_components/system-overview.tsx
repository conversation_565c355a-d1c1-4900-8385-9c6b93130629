import {
  AlertTriangle,
  BarChart3,
  CheckCircle,
  Clock,
  TrendingUp,
  Users
} from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";

const stats = [
  {
    icon: TrendingUp,
    title: "质检覆盖率",
    value: "100%",
    description: "全量通话质检覆盖",
    progress: 100,
    color: "text-green-600"
  },
  {
    icon: Clock,
    title: "处理效率",
    value: "分钟级",
    description: "从数天缩短至分钟级反馈",
    progress: 95,
    color: "text-blue-600"
  },
  {
    icon: Users,
    title: "多角色支持",
    value: "4+",
    description: "坐席、班组长、复核员、主管",
    progress: 100,
    color: "text-purple-600"
  },
  {
    icon: CheckCircle,
    title: "AI准确率",
    value: "95%+",
    description: "持续学习优化的AI引擎",
    progress: 95,
    color: "text-orange-600"
  }
];

const benefits = [
  {
    title: "效率提升",
    description: "自动化质检将工作效率提升10倍以上",
    icon: TrendingUp,
    color: "text-green-600"
  },
  {
    title: "成本降低",
    description: "减少人工质检成本，优化资源配置",
    icon: BarChart3,
    color: "text-blue-600"
  },
  {
    title: "风险管控",
    description: "实时监控预警，变事后补救为事中干预",
    icon: AlertTriangle,
    color: "text-orange-600"
  },
  {
    title: "质量保障",
    description: "统一标准，客观公正的质检评价体系",
    icon: CheckCircle,
    color: "text-purple-600"
  }
];

export function SystemOverview() {
  return (
    <section className="relative py-20 md:py-32 overflow-hidden">
      <div className="container mx-auto px-4">
        <div className="mx-auto mb-16 max-w-3xl text-center">
          {/* <Badge 
            variant="outline" 
            className="mb-4 px-3 py-1 animate-in fade-in slide-in-from-bottom-2 duration-500"
          >
            系统概览
          </Badge> */}
          <h2 className="mb-4 text-3xl font-bold tracking-tight md:text-4xl animate-in fade-in slide-in-from-bottom-3 duration-500">
            重塑客服质检工作范式
          </h2>
          <p className="text-lg text-muted-foreground animate-in fade-in slide-in-from-bottom-4 duration-500">
            通过AI驱动的智能质检系统，解决传统质检模式中的效率、成本、风险和管理难题
          </p>
        </div>
        {/* 核心数据指标 */}
        <div className="mb-16 grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {stats.map((stat, index) => (
            <Card 
              key={index} 
              className="group relative overflow-hidden text-center transition-all duration-500 hover:scale-105 hover:shadow-lg hover:shadow-primary/10">
              <div className="absolute inset-0 bg-gradient-to-br from-transparent via-primary/5 to-transparent opacity-0 transition-opacity duration-500 group-hover:opacity-100"></div>
              <CardHeader className="relative pb-2">
                <div className={`mx-auto rounded-lg bg-gradient-to-br from-muted to-muted/80 p-3 w-fit transition-transform duration-500 group-hover:scale-110 ${stat.color}`}>
                  <stat.icon className="h-6 w-6 transition-transform duration-500 group-hover:rotate-12" />
                </div>
              </CardHeader>
              <CardContent className="relative space-y-2">
                <div className="text-3xl font-bold transition-all duration-500 group-hover:text-4xl">{stat.value}</div>
                <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                <CardDescription className="text-xs">{stat.description}</CardDescription>
                <div className="mt-3">
                  <Progress 
                    value={stat.progress} 
                    className="h-2 transition-all duration-500 group-hover:h-3" 
                  />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>        
        {/* 核心价值 */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {benefits.map((benefit, index) => (
            <Card 
              key={index} 
              className="group relative overflow-hidden transition-all duration-500 hover:shadow-lg hover:shadow-primary/10 hover:-translate-y-1"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5 opacity-0 transition-opacity duration-500 group-hover:opacity-100"></div>
              <CardHeader className="relative pb-4">
                <div className={`rounded-lg bg-gradient-to-br from-muted to-muted/80 p-2 w-fit transition-transform duration-500 group-hover:scale-110 group-hover:rotate-12 ${benefit.color}`}>
                  <benefit.icon className="h-5 w-5 transition-transform duration-500" />
                </div>
                <CardTitle className="text-lg transition-colors duration-500 group-hover:text-primary">{benefit.title}</CardTitle>
              </CardHeader>
              <CardContent className="relative">
                <CardDescription className="text-sm leading-relaxed transition-all duration-500 group-hover:text-foreground/80">
                  {benefit.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>      
      {/* 背景装饰 */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute left-1/4 top-1/4 h-72 w-72 rounded-full bg-primary/5 blur-3xl animate-pulse"></div>
        <div className="absolute right-1/4 bottom-1/4 h-64 w-64 rounded-full bg-secondary/10 blur-3xl animate-pulse [animation-delay:1500ms]"></div>
      </div>
    </section>
  );
}