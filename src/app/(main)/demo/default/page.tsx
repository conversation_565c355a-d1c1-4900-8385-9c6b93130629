import { CTASection } from "./_components/cta-section";
import { FeatureCards } from "./_components/feature-cards";
import { HeroSection } from "./_components/hero-section";
import { SystemOverview } from "./_components/system-overview";
import { UserRoles } from "./_components/user-roles";

export default function Page() {
  return (
    <div className="@container/main">
      {/* 英雄区域 - 产品介绍和核心价值 */}
      <HeroSection />
      {/* 系统概览 - 核心数据和价值展示 */}
      <SystemOverview />
      {/* 功能特性 - 四大核心支柱 */}
      <FeatureCards />
      {/* 用户角色 - 多角色协同体验 */}
      <UserRoles />
      {/* 行动召唤 - 引导用户体验 */}
      <CTASection />
    </div>
  );
}

