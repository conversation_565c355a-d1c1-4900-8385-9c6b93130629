import { BarChart3 } from "lucide-react"
import { Badge } from "@/components/ui/badge"

/**
 * 页面头部组件属性接口
 */
interface PageHeaderProps {
  currentRole: "agent" | "team-leader" | "supervisor"
}

/**
 * 动态页面头部组件
 * 根据当前角色显示不同的标题、副标题和徽章
 */
export function PageHeader({ currentRole }: PageHeaderProps) {
  const getHeaderConfig = () => {
    switch (currentRole) {
      case "agent":
        return {
          title: "我的质检成绩",
          description: "查看个人质检成绩记录",
          badge: "个人视角"
        }
      case "team-leader":
        return {
          title: "团队质检成绩",
          description: "查看团队质检成绩记录",
          badge: "团队视角"
        }
      case "supervisor":
        return {
          title: "质检成绩管理",
          description: "查看和管理所有质检成绩记录",
          badge: "管理视角"
        }
      default:
        return {
          title: "质检成绩",
          description: "查看质检成绩记录",
          badge: "默认视角"
        }
    }
  }

  const config = getHeaderConfig()

  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-3">
          <div className="flex size-12 items-center justify-center rounded-lg bg-primary/10">
            <BarChart3 className="size-6 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold">{config.title}</h1>
            <p className="text-muted-foreground mt-1">{config.description}</p>
          </div>
        </div>
      </div>
      <Badge variant="outline" className="text-sm">
        {config.badge}
      </Badge>
    </div>
  )
}