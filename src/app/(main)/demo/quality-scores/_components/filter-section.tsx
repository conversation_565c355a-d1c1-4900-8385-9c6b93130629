"use client"

import { useState } from "react"
import { Search, Calendar, Filter } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"

/**
 * 筛选器组件属性接口
 */
interface FilterSectionProps {
  currentRole: "agent" | "team-leader" | "supervisor"
}

/**
 * 统一搜索筛选器组件
 * 根据角色动态显示不同的筛选选项
 */
export function FilterSection({ currentRole }: FilterSectionProps) {
  const [scoreRange, setScoreRange] = useState([0, 100])

  const handleSearch = () => {
    console.log("执行搜索", { currentRole })
  }

  const handleReset = () => {
    console.log("重置筛选条件")
    setScoreRange([0, 100])
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Filter className="size-5" />
          筛选条件
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {/* 记录编号 - 所有角色 */}
          <div className="space-y-2">
            <Label htmlFor="recordNumber">记录编号</Label>
            <Input
              id="recordNumber"
              placeholder="输入记录编号"
              className="w-full"
            />
          </div>

          {/* 客户号码 - 所有角色 */}
          <div className="space-y-2">
            <Label htmlFor="customerPhone">客户号码</Label>
            <Input
              id="customerPhone"
              placeholder="输入客户号码"
              className="w-full"
            />
          </div>

          {/* 通话时间范围 - 所有角色 */}
          <div className="space-y-2">
            <Label>通话开始时间</Label>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" className="w-full justify-start">
                <Calendar className="size-4 mr-2" />
                选择日期
              </Button>
            </div>
          </div>

          {/* 质检结果 - 所有角色 */}
          <div className="space-y-2">
            <Label>质检结果</Label>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="选择质检结果" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部</SelectItem>
                <SelectItem value="pass">合格</SelectItem>
                <SelectItem value="fail">不合格</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 申诉状态 - 所有角色 */}
          <div className="space-y-2">
            <Label>申诉状态</Label>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="选择申诉状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部</SelectItem>
                <SelectItem value="none">未申诉</SelectItem>
                <SelectItem value="pending">申诉中</SelectItem>
                <SelectItem value="success">申诉成功</SelectItem>
                <SelectItem value="failed">申诉失败</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 坐席选择 - 班组长和主管 */}
          {(currentRole === "team-leader" || currentRole === "supervisor") && (
            <div className="space-y-2">
              <Label>坐席</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="选择坐席" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部坐席</SelectItem>
                  <SelectItem value="agent1">张小明</SelectItem>
                  <SelectItem value="agent2">李小红</SelectItem>
                  <SelectItem value="agent3">王小强</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {/* 班组选择 - 仅主管 */}
          {currentRole === "supervisor" && (
            <div className="space-y-2">
              <Label>所属班组</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="选择班组" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部班组</SelectItem>
                  <SelectItem value="team1">客服部-A班组</SelectItem>
                  <SelectItem value="team2">客服部-B班组</SelectItem>
                  <SelectItem value="team3">售后部-A班组</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
        </div>

        {/* 得分范围滑块 */}
        <div className="mt-6 space-y-3">
          <Label>得分范围: {scoreRange[0]} - {scoreRange[1]}</Label>
          <Slider
            value={scoreRange}
            onValueChange={setScoreRange}
            max={100}
            min={0}
            step={1}
            className="w-full"
          />
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center gap-3 mt-6">
          <Button onClick={handleSearch} className="flex items-center gap-2">
            <Search className="size-4" />
            搜索
          </Button>
          <Button variant="outline" onClick={handleReset}>
            重置
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}