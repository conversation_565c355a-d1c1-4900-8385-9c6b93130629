import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent } from "@/components/ui/card"
import { Users, User, Shield } from "lucide-react"

/**
 * 角色切换器组件属性接口
 */
interface RoleSwitcherProps {
  currentRole: "agent" | "team-leader" | "supervisor"
  onRoleChange: (role: "agent" | "team-leader" | "supervisor") => void
}

/**
 * 角色切换器组件
 * 仅主管可见，允许切换不同角色视角
 */
export function RoleSwitcher({ currentRole, onRoleChange }: RoleSwitcherProps) {
  const roleOptions = [
    {
      value: "agent" as const,
      label: "坐席视角",
      icon: User,
      description: "查看个人质检成绩"
    },
    {
      value: "team-leader" as const,
      label: "班组长视角", 
      icon: Users,
      description: "查看团队质检成绩"
    },
    {
      value: "supervisor" as const,
      label: "主管视角",
      icon: Shield,
      description: "管理所有质检成绩"
    }
  ]

  const currentOption = roleOptions.find(option => option.value === currentRole)

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">切换视角：</span>
          </div>
          <Select value={currentRole} onValueChange={onRoleChange}>
            <SelectTrigger className="w-48">
              <SelectValue>
                <div className="flex items-center gap-2">
                  {currentOption && <currentOption.icon className="size-4" />}
                  {currentOption?.label}
                </div>
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              {roleOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  <div className="flex items-center gap-2">
                    <option.icon className="size-4" />
                    <div>
                      <div className="font-medium">{option.label}</div>
                      <div className="text-xs text-muted-foreground">{option.description}</div>
                    </div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardContent>
    </Card>
  )
}