"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

/**
 * 质检成绩数据接口
 */
export interface QualityScore {
  id: string
  recordNumber: string
  taskName: string
  agentName: string
  teamName: string
  customerPhone: string
  callStartTime: Date
  callDuration: number
  finalScore: number
  aiScore?: number
  reviewScore?: number
  appealScore?: number
  qualityResult: "合格" | "不合格"
  appealStatus: "未申诉" | "申诉中" | "申诉成功" | "申诉失败"
  appealDeadline?: Date
  appealTime?: Date
}

/**
 * 成绩表格组件属性接口
 */
interface ScoresTableProps {
  currentRole: "agent" | "team-leader" | "supervisor"
}

/**
 * 模拟质检成绩数据
 */
const mockScoresData: QualityScore[] = [
  {
    id: "1",
    recordNumber: "QC20240121001",
    taskName: "日常质检任务",
    agentName: "张小明",
    teamName: "客服部-A班组",
    customerPhone: "138****5678",
    callStartTime: new Date("2024-01-21T09:15:30"),
    callDuration: 285,
    finalScore: 92,
    aiScore: 90,
    reviewScore: 92,
    qualityResult: "合格",
    appealStatus: "未申诉"
  },
  {
    id: "2", 
    recordNumber: "QC20240121002",
    taskName: "专项质检任务",
    agentName: "李小红",
    teamName: "客服部-A班组",
    customerPhone: "139****1234",
    callStartTime: new Date("2024-01-21T10:30:15"),
    callDuration: 420,
    finalScore: 78,
    aiScore: 75,
    reviewScore: 78,
    appealScore: 85,
    qualityResult: "不合格",
    appealStatus: "申诉成功",
    appealTime: new Date("2024-01-21T15:20:00")
  },
  {
    id: "3",
    recordNumber: "QC20240121003", 
    taskName: "日常质检任务",
    agentName: "王小强",
    teamName: "客服部-B班组",
    customerPhone: "137****9876",
    callStartTime: new Date("2024-01-21T14:45:22"),
    callDuration: 195,
    finalScore: 65,
    aiScore: 65,
    qualityResult: "不合格",
    appealStatus: "申诉中",
    appealDeadline: new Date("2024-01-24T23:59:59"),
    appealTime: new Date("2024-01-22T09:30:00")
  }
]

/**
 * 成绩记录表格组件
 * 根据角色权限动态显示/隐藏列
 */
export function ScoresTable({ currentRole }: ScoresTableProps) {
  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const formatDateTime = (date: Date) => {
    return date.toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit", 
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit"
    })
  }

  const getScoreEvolution = (score: QualityScore) => {
    const steps = []
    if (score.aiScore) steps.push(`AI初检: ${score.aiScore}分`)
    if (score.reviewScore) steps.push(`人工复核: ${score.reviewScore}分`)
    if (score.appealScore) steps.push(`申诉结果: ${score.appealScore}分`)
    return steps.join(" → ")
  }

  const getAppealInfo = (score: QualityScore) => {
    switch (score.appealStatus) {
      case "未申诉":
        return <Badge variant="secondary">未申诉</Badge>
      case "申诉中":
        return (
          <div className="space-y-1">
            <Badge variant="outline" className="text-orange-600">
              <Clock className="size-3 mr-1" />
              申诉中
            </Badge>
            {score.appealDeadline && (
              <div className="text-xs text-muted-foreground">
                截止: {formatDateTime(score.appealDeadline)}
              </div>
            )}
          </div>
        )
      case "申诉成功":
        return (
          <div className="space-y-1">
            <Badge variant="default" className="bg-green-600">申诉成功</Badge>
            {score.appealTime && (
              <div className="text-xs text-muted-foreground">
                {formatDateTime(score.appealTime)}
              </div>
            )}
          </div>
        )
      case "申诉失败":
        return (
          <div className="space-y-1">
            <Badge variant="destructive">申诉失败</Badge>
            {score.appealTime && (
              <div className="text-xs text-muted-foreground">
                {formatDateTime(score.appealTime)}
              </div>
            )}
          </div>
        )
    }
  }

  const handleViewDetails = (score: QualityScore) => {
    console.log("查看详情:", score)
  }

  const handleAppeal = (score: QualityScore) => {
    console.log("发起申诉:", score)
  }

  const canAppeal = (score: QualityScore) => {
    return currentRole === "agent" && 
           score.appealStatus === "未申诉" && 
           score.qualityResult === "不合格"
  }

  return (
    <Card>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-16">序号</TableHead>
                <TableHead>记录编号</TableHead>
                <TableHead>所属任务</TableHead>
                {currentRole !== "agent" && <TableHead>坐席</TableHead>}
                {currentRole === "supervisor" && <TableHead>所属班组</TableHead>}
                <TableHead>客户号码</TableHead>
                <TableHead>通话开始时间</TableHead>
                <TableHead>通话时长</TableHead>
                <TableHead>最终得分</TableHead>
                <TableHead>质检结果</TableHead>
                <TableHead>申诉信息</TableHead>
                <TableHead className="w-32">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockScoresData.map((score, index) => (
                <TableRow key={score.id}>
                  <TableCell>{index + 1}</TableCell>
                  <TableCell className="font-mono text-sm">{score.recordNumber}</TableCell>
                  <TableCell>{score.taskName}</TableCell>
                  {currentRole !== "agent" && <TableCell>{score.agentName}</TableCell>}
                  {currentRole === "supervisor" && <TableCell>{score.teamName}</TableCell>}
                  <TableCell className="font-mono">{score.customerPhone}</TableCell>
                  <TableCell>{formatDateTime(score.callStartTime)}</TableCell>
                  <TableCell>{formatDuration(score.callDuration)}</TableCell>
                  <TableCell>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <span className="font-semibold cursor-help">
                            {score.finalScore}分
                          </span>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="text-sm">{getScoreEvolution(score)}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </TableCell>
                  <TableCell>
                    <Badge variant={score.qualityResult === "合格" ? "default" : "destructive"}>
                      {score.qualityResult}
                    </Badge>
                  </TableCell>
                  <TableCell>{getAppealInfo(score)}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleViewDetails(score)}
                      >
                        <Eye className="size-4" />
                      </Button>
                      {canAppeal(score) && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleAppeal(score)}
                        >
                          <AlertTriangle className="size-4" />
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
}