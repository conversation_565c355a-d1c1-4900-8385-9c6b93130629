"use client"

import { useState } from "react"
import { PageHeader } from "./_components/page-header"
import { RoleSwitcher } from "./_components/role-switcher"
import { FilterSection } from "./_components/filter-section"
import { ScoresTable } from "./_components/scores-table"
import { PaginationSection } from "./_components/pagination-section"

/**
 * 用户角色接口
 */
export interface UserRole {
  type: "agent" | "team-leader" | "supervisor"
  name: string
  teamName?: string
}

/**
 * 质检成绩页面
 * 多角色复用的质检成绩查询中心
 */
export default function QualityScoresPage() {
  // 模拟当前用户角色（实际项目中应从认证系统获取）
  const [currentUser] = useState<UserRole>({
    type: "supervisor",
    name: "李主管",
    teamName: "质检部"
  })

  // 当前视角角色（主管可以切换视角）
  const [currentRole, setCurrentRole] = useState<UserRole["type"]>(currentUser.type)

  return (
    <div className="container mx-auto p-6 space-y-6">
      <PageHeader currentRole={currentRole} />
      
      {/* 角色切换器 - 仅主管可见 */}
      {currentUser.type === "supervisor" && (
        <RoleSwitcher 
          currentRole={currentRole}
          onRoleChange={setCurrentRole}
        />
      )}
      
      <FilterSection currentRole={currentRole} />
      <ScoresTable currentRole={currentRole} />
      <PaginationSection />
    </div>
  )
}