import {
  ShoppingBag,
  Forklift,
  Mail,
  MessageSquare,
  Calendar,
  Kanban,
  ReceiptText,
  Users,
  Lock,
  Fingerprint,
  SquareArrowUpRight,
  LayoutDashboard,
  ChartBar,
  Banknote,
  Gauge,
  GraduationCap,
  Eye,
  UserCheck,
  Shield,
  ClipboardCheck,
  FileText,
  MessageCircle,
  Bell,
  AlertTriangle,
  Target,
  Settings,
  BookOpen,
  Search,
  History,
  BarChart3,
  TrendingUp,
  FileBarChart,
  Database,
  Bot,
  Mic,
  type LucideIcon,
} from "lucide-react";

export interface NavSubItem {
  title: string;
  url: string;
  icon?: LucideIcon;
  comingSoon?: boolean;
  newTab?: boolean;
  isNew?: boolean;
}

export interface NavMainItem {
  title: string;
  url: string;
  icon?: LucideIcon;
  subItems?: NavSubItem[];
  comingSoon?: boolean;
  newTab?: boolean;
  isNew?: boolean;
}

export interface NavGroup {
  id: number;
  label?: string;
  items: NavMainItem[];
}

export const sidebarItems: NavGroup[] = [
  {
    id: 1,
    label: "导航菜单",
    items: [
      {
        title: "主页",
        url: "/demo/default",
        icon: LayoutDashboard,
      },
      {
        title: "首页",
        url: "/demo/not-found",
        icon: Eye,
        subItems: [
          {
            title: "坐席员视角",
            url: "/demo/agent-dashboard",
            icon: Eye,
          },
          {
            title: "班组长视角",
            url: "/demo/team-leader-dashboard",
            icon: UserCheck,
          },
          {
            title: "复核员视角",
            url: "/demo/reviewer-dashboard",
            icon: ClipboardCheck,
          },
          {
            title: "质检主管视角",
            url: "/demo/supervisor-dashboard",
            icon: Shield,
          },
        ],
      },
      {
        title: "质检工作台",
        url: "/demo/not-found",
        icon: ClipboardCheck,
        subItems: [
          {
            title: "我的复核任务",
            url: "/demo/not-found",
            icon: ClipboardCheck,
          },
          {
            title: "申诉处理",
            url: "/demo/not-found",
            icon: MessageCircle,
          },
          {
            title: "质检成绩管理",
            url: "/demo/quality-scores",
            icon: FileText,
          },
          {
            title: "质检详情页",
            url: "/demo/quality-inspection-detail",
            icon: FileText,
          },
          {
            title: "通知中心",
            url: "/demo/notification-center",
            icon: Bell,
          },
          {
            title: "实时预警中心",
            url: "/demo/not-found",
            icon: AlertTriangle,
          },
          {
            title: "预警跟进中心",
            url: "/demo/not-found",
            icon: Target,
          },
        ],
      },
      {
        title: "质检管理",
        url: "/demo/not-found",
        icon: Settings,
        subItems: [
          {
            title: "质检规则管理",
            url: "/demo/not-found",
            icon: Settings,
          },
          {
            title: "质检方案管理",
            url: "/demo/not-found",
            icon: FileText,
          },
          {
            title: "质检计划管理",
            url: "/demo/not-found",
            icon: Calendar,
          },
          {
            title: "质检任务管理",
            url: "/demo/not-found",
            icon: Kanban,
          },
          {
            title: "复核策略配置",
            url: "/demo/not-found",
            icon: Settings,
          },
          {
            title: "质检词库管理",
            url: "/demo/not-found",
            icon: BookOpen,
          },
          {
            title: "质检明细查询",
            url: "/demo/not-found",
            icon: Search,
          },
          {
            title: "历史预警查询",
            url: "/demo/not-found",
            icon: History,
          },
        ],
      },
      {
        title: "统计分析",
        url: "/demo/not-found",
        icon: BarChart3,
        subItems: [
          {
            title: "质检运营总览",
            url: "/demo/not-found",
            icon: BarChart3,
          },
          {
            title: "服务质量深度分析",
            url: "/demo/not-found",
            icon: TrendingUp,
          },
          {
            title: "复核工作分析报告",
            url: "/demo/not-found",
            icon: FileBarChart,
          },
          {
            title: "坐席申诉洞察报告",
            url: "/demo/not-found",
            icon: ChartBar,
          },
        ],
      },
      {
        title: "系统管理",
        url: "/demo/not-found",
        icon: Database,
        subItems: [
          {
            title: "数据源管理",
            url: "/demo/not-found",
            icon: Database,
          },
          {
            title: "大模型管理",
            url: "/demo/not-found",
            icon: Bot,
          },
          {
            title: "语音识别引擎管理",
            url: "/demo/not-found",
            icon: Mic,
          },
          {
            title: "通知渠道管理",
            url: "/demo/not-found",
            icon: Bell,
          },
        ],
      },
    ],
  },
];