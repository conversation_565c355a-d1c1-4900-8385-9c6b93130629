首页
    坐席员视角
    班组长视角
    复核员视角
    质检主管视角
质检工作台
   我的复核任务
   申诉处理
   质检成绩管理
   通知中心
   实时预警中心
   预警跟进中心
质检管理
   质检规则管理
   质检方案管理
   质检计划管理
   质检任务管理
   复核策略配置
   质检词库管理
   质检明细查询
   历史预警查询
统计分析
   质检运营总览
   服务质量深度分析
   复核工作分析报告
   坐席申诉洞察报告
系统管理
   数据源管理
   大模型管理
   语音识别引擎管理
   通知渠道管理

----------------------------------------------------------------------------------------------------------------------------

请为本项目编写一份完整的页面开发规范文档，文档应包含以下具体要求：

## 1. 页面目录结构规范
- **创建位置**：所有新页面必须在 `src/app/(main)/demo/` 目录下创建独立的子目录
- **目录命名**：使用 kebab-case 命名规则（如：`user-management`、`data-analysis`）
- **必需文件结构**：
  ```
  src/app/(main)/demo/[page-name]/
  ├── _components/          # 页面专用组件目录
  │   ├── component-a.tsx   # 具体组件文件
  │   └── component-b.tsx
  └── page.tsx             # 页面入口文件
  ```
- **参考示例**：参照 `src/app/(main)/demo/default/` 的目录结构

## 2. 组件拆分规范
- **强制要求**：每个页面必须将功能模块拆分为独立组件，放置在对应的 `_components/` 目录中
- **页面复杂度控制**：单个 `page.tsx` 文件不得超过 100 行代码
- **组件命名**：使用 PascalCase 命名，文件名使用 kebab-case（如：`UserTable` 组件保存为 `user-table.tsx`）
- **组件职责**：每个组件应有单一职责，避免过度复杂的组件

## 3. 原子组件使用规范
- **强制引用路径**：所有基础 UI 组件必须从 `src/components/` 目录引用
- **优先使用现有组件**：开发前先检查 `src/components/ui/` 中是否有可用组件
- **新组件创建流程**：
  1. 检查 shadcn/ui 是否提供该组件：`npx shadcn@latest add [component-name]`
  2. 如不存在，在 `src/components/ui/` 中创建新的原子组件
  3. 遵循项目的组件设计规范和 TypeScript 类型定义
- **禁止行为**：不得在页面中直接编写基础 UI 逻辑，必须通过组件封装

## 4. 页面类型一致性规范
- **页面分类**：明确定义以下页面类型的标准模板和规范
  - 列表页（List Pages）：数据表格、筛选、分页等
  - 表单页（Form Pages）：创建、编辑表单的布局和验证
  - 详情页（Detail Pages）：信息展示、操作按钮布局
  - 统计分析页（Analytics Pages）：图表、指标卡片布局
  - 仪表板页（Dashboard Pages）：综合信息展示
- **一致性要求**：同类型页面必须使用相同的布局模式、组件结构和交互方式
- **设计系统遵循**：严格按照项目的设计系统和 UI 规范执行

## 5. 技术规范要求
- **TypeScript**：所有组件必须使用 TypeScript，提供完整的类型定义
- **样式系统**：使用 Tailwind CSS，遵循项目的 CSS 变量系统
- **代码质量**：通过 ESLint 和 Prettier 检查，符合项目的代码规范
- **响应式设计**：确保所有页面在移动端和桌面端都有良好体验

请基于以上要求，结合项目现有的技术栈（Next.js 15、TypeScript、Tailwind CSS v4、shadcn/ui）和文件结构，编写一份详细的开发规范文档。

----------------------------------------------------------------------------------------------------------------------------

请根据以下需求文档，在 `src/app/(main)/demo/` 目录下创建一个质检主管首页的完整页面实现。

## 页面要求

### 1. 目录结构
- 在 `src/app/(main)/demo/supervisor-dashboard/` 创建页面
- 创建 `_components/` 目录存放页面专用组件
- 主页面文件：`page.tsx`

### 2. 页面布局（仪表板类型）
按照项目规范实现以下布局结构：

**页面头部组件** (`page-header.tsx`)
- 标题："质检主管视角"
- 副标题："欢迎回来，李主管 | 质检部 | 管理团队：3个"
- 使用 Shield 图标（来自 Lucide React）

**核心绩效指标组件** (`metrics-section.tsx`)
- 使用 4x2 网格布局的 MetricCard 组件
- 第一行指标：总质检量、总复核量、质检平均分、质检合格率
- 第二行指标：申诉率、申诉成功率、AI质检准确率
- 每个指标卡片需要显示数值、标题、趋势图标

**成绩趋势分析组件** (`trend-chart.tsx`)
- 使用折线图展示整体平均分和合格率趋势
- 图表标题："成绩趋势分析"
- 支持双折线显示

**排名统计组件** (`ranking-section.tsx`)
- 左右布局：团队排名 Top 5 和坐席排名 Top 5
- 使用表格组件展示排名数据
- 包含排名、名称、分数、合格率等列

**错误分析组件** (`error-analysis.tsx`)
- 左右布局：严重错误项 Top 5 和高频失分项 Top 5
- 使用卡片布局展示统计数据

**通知与申诉组件** (`notifications-appeals.tsx`)
- 左侧：通知公告列表
- 右侧：待处理申诉列表，每项包含"开始处理"按钮

### 3. 技术要求
- 严格遵循项目的 TypeScript 规范，为所有组件定义完整的 Props 接口
- 使用 `@/components/ui/` 中的基础组件（Card, Button, Badge, Table 等）
- 样式使用 Tailwind CSS，遵循项目的设计系统
- 组件文件使用 kebab-case 命名，组件函数使用 PascalCase
- 页面布局使用响应式设计，支持移动端和桌面端
- 模拟真实数据，使用合理的示例数据填充所有组件

### 4. 交互功能
- 排名表格支持点击跳转（暂时使用 console.log 模拟）
- 申诉列表的"开始处理"按钮支持点击事件
- 错误分析项支持点击查看详情

### 5. 数据结构
请为以下数据定义 TypeScript 接口：
- 绩效指标数据 (MetricData)
- 团队排名数据 (TeamRanking)
- 坐席排名数据 (AgentRanking)
- 错误分析数据 (ErrorAnalysis)
- 申诉数据 (AppealData)

请确保页面完全符合项目的开发规范，包括目录结构、组件拆分、代码质量等要求。

----------------------------------------------------------------------------------------------------------------------------

请根据《页面开发规范文档》严格实现班组长首页原型。具体要求如下：

## 开发任务
在 `src/app/(main)/demo/` 目录下创建 `team-leader-dashboard` 页面，实现完整的班组长工作台功能。

## 页面结构要求
严格按照规范创建以下目录结构：
```
src/app/(main)/demo/team-leader-dashboard/
├── _components/
│   ├── page-header.tsx          # 页面头部组件
│   ├── team-metrics-section.tsx # 团队核心绩效指标
│   ├── trend-and-ranking.tsx    # 趋势图表与成员排名
│   ├── error-analysis.tsx       # 错误分析统计
│   └── notifications-records.tsx # 通知公告与质检记录
└── page.tsx                     # 页面入口文件
```

## 具体功能实现

### 1. 页面头部 (page-header.tsx)
- 标题："班组长视角"
- 副标题："欢迎回来，李班长 | 客服部-A班组 | 团队成员：12人"
- 图标：Users（Lucide React）
- 徽章：显示团队排名 "#2"

### 2. 团队核心绩效指标 (team-metrics-section.tsx)
使用 MetricCard 网格布局（2x2），展示：
- 团队平均质检得分：85.2分，趋势上升
- 团队质检合格率：92.1%，趋势上升
- 团队申诉率：3.1%，趋势下降
- 团队申诉成功率：15.2%，趋势持平

### 3. 趋势图表与成员排名 (trend-and-ranking.tsx)
- 左侧（2/3宽度）：使用 ChartContainer 实现团队成绩趋势折线图
- 右侧（1/3宽度）：团队成员排名列表，显示 Top 5 和 Bottom 5

### 4. 错误分析统计 (error-analysis.tsx)
左右双卡片布局：
- 左侧：团队严重错误项 Top 5
- 右侧：团队高频失分项 Top 5

### 5. 通知公告与质检记录 (notifications-records.tsx)
左右双卡片布局：
- 左侧：通知公告列表
- 右侧：近期质检记录列表，支持点击跳转

## 技术规范要求
1. **严格遵循目录结构规范**：所有组件放在 `_components/` 目录
2. **TypeScript 类型安全**：为所有组件定义完整的 Props 接口
3. **UI 组件使用**：只使用 `@/components/ui/` 中的组件
4. **图表实现**：严格按照规范使用 ChartContainer 和 CSS 变量
5. **响应式设计**：使用 Tailwind 响应式类名，确保移动端适配
6. **暗黑模式适配**：所有组件在暗黑模式下正常显示
7. **代码质量**：通过 ESLint 和 Prettier 检查

## 数据模拟
创建合理的模拟数据，包括：
- 团队绩效指标数据
- 成员排名数据（至少10个成员）
- 趋势图表数据（近7天）
- 错误分析数据
- 通知和质检记录数据

## 验证要求
- [ ] 目录结构符合规范
- [ ] 组件拆分合理，单个文件不超过100行
- [ ] TypeScript 类型定义完整
- [ ] 图表在暗黑模式下正常显示
- [ ] 响应式布局在移动端和桌面端都正常
- [ ] 所有交互功能正常工作

请完整实现这个页面，确保每个组件都符合开发规范要求。

----------------------------------------------------------------------------------------------------------------------------

请根据《页面开发规范文档》严格实现复核员首页原型。具体要求如下：

## 开发任务
在 `src/app/(main)/demo/` 目录下创建 `reviewer-dashboard` 页面，实现完整的复核员工作台功能。

## 页面结构要求
严格按照规范创建以下目录结构：
```
src/app/(main)/demo/reviewer-dashboard/
├── _components/
│   ├── page-header.tsx          # 页面头部组件
│   ├── work-metrics-section.tsx # 核心工作指标
│   ├── workload-trend-chart.tsx # 工作量趋势分析图表
│   ├── notifications-section.tsx # 通知公告
│   └── pending-tasks-list.tsx   # 待复核任务列表
└── page.tsx                     # 页面入口文件
```

## 具体功能实现

### 1. 页面头部 (page-header.tsx)
- 标题："复核员视角"
- 副标题："欢迎回来，李复核 | 质检部-复核组"
- 图标：UserCheck（Lucide React）
- 徽章：显示状态 "在线"

### 2. 核心工作指标 (work-metrics-section.tsx)
使用 MetricCard 网格布局（2x2），展示：
- 复核总量：本周完成的复核任务总数，趋势上升
- 待复核量：当前待处理的任务数，趋势下降
- 日均复核量：平均工作效率，趋势稳定
- AI结果纠错数：修正AI初检结果的次数，趋势上升

### 3. 工作量趋势分析图表 (workload-trend-chart.tsx)
- 使用 ChartContainer 实现双折线图
- 展示近7天的"完成复核数"和"AI纠错数量"两条趋势线
- 图表标题："工作量趋势分析"
- 支持鼠标悬浮显示详细数据

### 4. 通知公告 (notifications-section.tsx)
- 单卡片布局
- 展示与复核员工作相关的通知信息
- 包含通知类型、标题、时间等信息

### 5. 待复核任务列表 (pending-tasks-list.tsx)
- 单卡片布局
- 标题："待复核任务"
- 展示最高优先级的待复核任务（5-8条）
- 每条任务包含：记录ID、被检坐席、客户电话、AI初检分数、触发原因
- 每条任务末尾有"开始复核"按钮，点击时使用 console.log 模拟跳转

## 技术规范要求
1. **严格遵循目录结构规范**：所有组件放在 `_components/` 目录
2. **TypeScript 类型安全**：为所有组件定义完整的 Props 接口
3. **UI 组件使用**：只使用 `@/components/ui/` 中的组件
4. **图表实现**：严格按照规范使用 ChartContainer 和 CSS 变量，确保暗黑模式适配
5. **响应式设计**：使用 Tailwind 响应式类名，确保移动端适配
6. **代码质量**：通过 ESLint 和 Prettier 检查

## 数据模拟
创建合理的模拟数据，包括：
- 工作指标数据（复核总量、待复核量等）
- 工作量趋势数据（近7天的完成数和纠错数）
- 通知公告数据（3-4条相关通知）
- 待复核任务数据（5-8条任务记录）

## 验证要求
- [ ] 目录结构符合规范
- [ ] 组件拆分合理，单个文件不超过100行
- [ ] TypeScript 类型定义完整
- [ ] 图表在暗黑模式下正常显示
- [ ] 响应式布局在移动端和桌面端都正常
- [ ] 所有交互功能正常工作（按钮点击事件）

请完整实现这个页面，确保每个组件都符合开发规范要求。

----------------------------------------------------------------------------------------------------------------------------

请根据《页面开发规范文档》严格实现客服坐席首页原型。具体要求如下：

## 开发任务
在 `src/app/(main)/demo/` 目录下创建 `agent-dashboard` 页面，实现完整的客服坐席工作台功能。

## 页面结构要求
严格按照规范创建以下目录结构：
```
src/app/(main)/demo/agent-dashboard/
├── _components/
│   ├── page-header.tsx          # 页面头部组件
│   ├── performance-metrics-section.tsx # 核心绩效指标（6个卡片）
│   ├── score-trend-chart.tsx    # 成绩趋势分析图表
│   ├── error-analysis-section.tsx # 错误分析统计（左右双卡片）
│   ├── notifications-section.tsx # 通知公告
│   └── recent-scores-list.tsx   # 近期成绩列表
└── page.tsx                     # 页面入口文件
```

## 具体功能实现

### 1. 页面头部 (page-header.tsx)
- 标题："坐席员视角"
- 副标题："欢迎回来，张小明 | 客服部-A组"
- 图标：User（Lucide React）
- 徽章：显示状态 "在线"

### 2. 核心绩效指标 (performance-metrics-section.tsx)
使用 MetricCard 网格布局（3x2），展示6个关键指标：
- 平均分数：当前周期的质检平均分，趋势指示器
- 团队排名：在团队中的排名位置，趋势变化
- 质检合格率：合格通话占比，趋势上升/下降
- 一次性通过率：无需申诉的通话占比，趋势稳定
- 申诉次数：发起申诉的总次数，趋势变化
- 申诉成功率：申诉成功的比例，趋势指示

每个卡片包含：
- 主要数值和趋势指示器
- 与上周期的对比值（如 "+2.5%" 或 "-1.2%"）
- 帮助图标（HelpCircle），悬浮显示指标定义

### 3. 成绩趋势分析图表 (score-trend-chart.tsx)
- 使用 ChartContainer 实现平滑折线图
- 图表标题："成绩趋势分析"
- 展示近14天的每日质检平均分数变化
- 支持鼠标悬浮显示具体日期和分数
- 确保暗黑模式下正常显示

### 4. 错误分析统计 (error-analysis-section.tsx)
左右双卡片布局：
- 左侧卡片："严重错误项 Top5"
  - 列表展示最常触犯的严重级别质检规则
  - 包含错误类型、触犯次数、影响分数
- 右侧卡片："高频失分项 Top5"
  - 列表展示导致失分最多的规则
  - 包含失分项、失分次数、累计扣分

### 5. 通知公告 (notifications-section.tsx)
- 单卡片布局
- 展示与坐席相关的最新通知（3-4条）
- 包含通知类型、标题、时间等信息
- 支持点击跳转（使用 console.log 模拟）

### 6. 近期成绩列表 (recent-scores-list.tsx)
- 单卡片布局
- 标题："近期成绩"
- 展示最近的质检记录（5-8条）
- 每条记录包含：记录ID、最终得分、质检结果、申诉状态、通话基本信息
- 每条记录支持点击查看详情（使用 console.log 模拟跳转）

## 技术规范要求
1. **严格遵循目录结构规范**：所有组件放在 `_components/` 目录
2. **TypeScript 类型安全**：为所有组件定义完整的 Props 接口
3. **UI 组件使用**：只使用 `@/components/ui/` 中的组件
4. **图表实现**：严格按照规范使用 ChartContainer 和 CSS 变量，确保暗黑模式适配
5. **响应式设计**：使用 Tailwind 响应式类名，确保移动端适配
6. **代码质量**：通过 ESLint 和 Prettier 检查

## 数据模拟
创建合理的模拟数据，包括：
- 绩效指标数据（6个核心指标的数值和趋势）
- 成绩趋势数据（近14天的分数变化）
- 错误分析数据（严重错误和高频失分项各5条）
- 通知公告数据（3-4条相关通知）
- 近期成绩数据（5-8条质检记录）

## 验证要求
- [ ] 目录结构符合规范
- [ ] 组件拆分合理，单个文件不超过100行
- [ ] TypeScript 类型定义完整
- [ ] 图表在暗黑模式下正常显示
- [ ] 响应式布局在移动端和桌面端都正常
- [ ] 所有交互功能正常工作（点击事件、悬浮提示）
- [ ] 帮助图标悬浮显示指标定义

请完整实现这个页面，确保每个组件都符合开发规范要求。

----------------------------------------------------------------------------------------------------------------------------

请根据《页面开发规范文档》严格实现质检成绩页原型。具体要求如下：

## 开发任务
在 `src/app/(main)/demo/` 目录下创建 `quality-scores` 页面，实现完整的多角色质检成绩查询功能。

## 页面结构要求
严格按照规范创建以下目录结构：
```
src/app/(main)/demo/quality-scores/
├── _components/
│   ├── page-header.tsx          # 动态页面头部组件
│   ├── role-switcher.tsx        # 角色切换器（仅主管可见）
│   ├── filter-section.tsx       # 统一搜索筛选器
│   ├── scores-table.tsx         # 成绩记录表格
│   └── pagination-section.tsx   # 分页组件
└── page.tsx                     # 页面入口文件
```

## 具体功能实现

### 1. 页面头部组件 (`page-header.tsx`)
- **动态标题和副标题**：
  - 坐席视角: "我的质检成绩" / "查看个人质检成绩记录"
  - 班组长视角: "团队质检成绩" / "查看团队质检成绩记录"
  - 主管视角: "质检成绩管理" / "查看和管理所有质检成绩记录"
- **图标**：使用 `BarChart3` 图标
- **动态徽章**：根据当前角色显示不同标签（"个人视角"/"团队视角"/"管理视角"）

### 2. 角色切换器组件 (`role-switcher.tsx`)
- **仅主管可见**：使用条件渲染，只有主管角色才显示此组件
- **切换选项**：坐席视角、班组长视角、主管视角
- **状态管理**：使用 `useState` 管理当前选中的角色视角
- **样式**：使用 `Select` 组件实现下拉选择

### 3. 筛选器组件 (`filter-section.tsx`)
- **动态筛选字段**：根据当前角色显示不同的筛选选项
  - 坐席：记录编号、客户号码、通话时间范围、得分范围、质检结果、申诉状态
  - 班组长：增加坐席选择器（限本团队成员）
  - 主管：增加坐席选择器、班组选择器（全局范围）
- **筛选组件**：使用 `Input`、`Select`、`DatePicker`、`Slider` 等组件
- **布局**：使用 `Card` 包装，响应式网格布局

### 4. 成绩表格组件 (`scores-table.tsx`)
- **动态列显示**：根据角色权限动态显示/隐藏列
  - 坐席：隐藏"坐席"和"所属班组"列
  - 班组长：隐藏"所属班组"列（因为都是本团队）
  - 主管：显示所有列
- **表格列定义**：
  - 序号、记录编号、所属任务、坐席、所属班组、客户号码（脱敏）
  - 通话开始时间、通话时长、最终得分、质检结果、申诉信息、操作
- **得分显示**：使用 `Tooltip` 展示分数演进过程（AI → 复核 → 申诉）
- **质检结果**：使用 `Badge` 显示"合格"/"不合格"状态
- **申诉信息**：复合信息列，显示申诉状态、结果和关键时间
- **操作按钮**：
  - 查看详情（所有角色）：使用 `Eye` 图标
  - 发起申诉（仅坐席）：使用 `AlertTriangle` 图标，条件显示

### 5. 分页组件 (`pagination-section.tsx`)
- **分页信息**：显示总记录数、当前页、每页条数
- **分页控件**：使用 `Pagination` 组件
- **每页条数选择**：支持 10、20、50、100 条选项

## 数据模拟
创建完整的 TypeScript 接口和模拟数据：

```typescript
interface QualityScore {
  id: string
  recordNumber: string
  taskName: string
  agentName: string
  teamName: string
  customerPhone: string // 脱敏处理
  callStartTime: Date
  callDuration: number // 秒
  finalScore: number
  aiScore?: number
  reviewScore?: number
  appealScore?: number
  qualityResult: "合格" | "不合格"
  appealStatus: "未申诉" | "申诉中" | "申诉成功" | "申诉失败"
  appealDeadline?: Date
  appealTime?: Date
}

interface UserRole {
  type: "agent" | "team-leader" | "supervisor"
  name: string
  teamName?: string
}
```

## 技术规范要求
1. **严格遵循目录结构规范**：所有组件放在 `_components/` 目录
2. **TypeScript 类型安全**：为所有组件定义完整的 Props 接口
3. **UI 组件使用**：只使用 `@/components/ui/` 中的组件
4. **响应式设计**：使用 Tailwind 响应式类名，确保移动端适配
5. **权限控制**：通过条件渲染实现不同角色的权限隔离
6. **状态管理**：使用 React hooks 管理组件状态
7. **交互功能**：所有按钮点击事件使用 `console.log` 模拟

## 验证要求
- [ ] 目录结构符合规范
- [ ] 组件拆分合理，单个文件不超过100行
- [ ] TypeScript 类型定义完整
- [ ] 角色切换功能正常工作
- [ ] 筛选器根据角色动态显示字段
- [ ] 表格列根据角色权限动态显示
- [ ] 分页功能正常工作
- [ ] 响应式布局在移动端和桌面端都正常
- [ ] 所有交互功能正常工作

请完整实现这个页面，确保每个组件都符合开发规范要求，并实现完整的多角色权限控制逻辑。

----------------------------------------------------------------------------------------------------------------------------

请根据《页面开发规范文档》严格实现通知中心页面原型。具体要求如下：

## 开发任务
在 `src/app/(main)/demo/` 目录下创建 `notification-center` 页面，实现完整的多角色通知管理功能。

## 页面结构要求
严格按照规范创建以下目录结构：
```
src/app/(main)/demo/notification-center/
├── _components/
│   ├── page-header.tsx          # 页面头部组件
│   ├── category-sidebar.tsx     # 左侧分类导航
│   ├── filter-section.tsx       # 搜索筛选器
│   ├── batch-operations.tsx     # 批量操作栏
│   ├── notification-list.tsx    # 通知列表
│   └── pagination-section.tsx   # 分页组件
└── page.tsx                     # 页面入口文件
```

## 具体功能实现

### 1. 页面头部组件 (`page-header.tsx`)
- **标题**："通知中心"
- **副标题**："查看和管理系统通知消息"
- **图标**：使用 `Bell` 图标（Lucide React）
- **动态徽章**：显示未读消息总数（如："12条未读"）
- **快捷操作按钮**：
  - "全部已读"按钮（使用 `CheckCheck` 图标）
  - "清空通知"按钮（使用 `Trash2` 图标）

### 2. 分类导航组件 (`category-sidebar.tsx`)
- **左侧固定宽度导航栏**（约1/4宽度）
- **分类项目**：
  - 全部通知（显示总数）
  - 任务提醒（显示未读数）
  - 结果通知（显示未读数）
  - 系统公告（显示未读数）
- **样式要求**：
  - 使用 `Card` 包装整个导航区域
  - 当前选中分类高亮显示
  - 每个分类项显示未读数量徽章

### 3. 搜索筛选器组件 (`filter-section.tsx`)
- **筛选字段**：
  - 搜索内容（Input 输入框）
  - 读取状态（Select：全部/已读/未读）
  - 时间范围（DatePicker 日期选择器）
- **布局**：使用响应式网格布局，移动端垂直排列
- **操作按钮**：搜索、重置按钮

### 4. 批量操作栏组件 (`batch-operations.tsx`)
- **选择控制**：全选/取消全选复选框
- **批量操作按钮**：
  - 标记为已读（`Eye` 图标）
  - 标记为未读（`EyeOff` 图标）
  - 删除选中（`Trash2` 图标）
- **选中状态显示**：显示当前选中的通知数量

### 5. 通知列表组件 (`notification-list.tsx`)
- **列表项结构**：每条通知包含
  - 复选框（用于批量操作）
  - 已读/未读状态指示点
  - 通知类型徽章（任务提醒/结果通知/系统公告）
  - 标题和内容摘要
  - 时间戳（相对时间显示）
  - 快捷操作按钮（标记已读/删除）
- **视觉区分**：
  - 未读通知使用更醒目的背景色
  - 已读通知使用较淡的文字颜色
- **交互功能**：
  - 点击通知内容区域自动标记为已读
  - 支持跳转到相关详情页面

### 6. 分页组件 (`pagination-section.tsx`)
- **分页信息**：显示总通知数、当前页、每页条数
- **分页控件**：使用 `Pagination` 组件
- **每页条数选择**：支持 10、20、50 条选项

## 数据模拟
创建完整的 TypeScript 接口和模拟数据：

```typescript
interface Notification {
  id: string
  type: "task-reminder" | "result-notification" | "system-announcement"
  title: string
  content: string
  isRead: boolean
  createdAt: Date
  relatedUrl?: string
  priority: "low" | "medium" | "high"
}

interface NotificationCategory {
  key: string
  label: string
  count: number
  unreadCount: number
}
```

## 布局要求
- **主布局**：左右双栏布局（1:3比例）
- **左侧**：分类导航（固定宽度）
- **右侧**：主内容区域（筛选器 + 批量操作 + 通知列表 + 分页）
- **响应式适配**：移动端改为上下布局

## 技术规范要求
1. **严格遵循目录结构规范**：所有组件放在 `_components/` 目录
2. **TypeScript 类型安全**：为所有组件定义完整的 Props 接口
3. **UI 组件使用**：只使用 `@/components/ui/` 中的组件
4. **响应式设计**：使用 Tailwind 响应式类名，确保移动端适配
5. **状态管理**：使用 React hooks 管理选中状态、筛选状态等
6. **交互功能**：所有按钮点击事件使用 `console.log` 模拟

## 验证要求
- [ ] 目录结构符合规范
- [ ] 组件拆分合理，单个文件不超过100行
- [ ] TypeScript 类型定义完整
- [ ] 左右双栏布局在桌面端正常显示
- [ ] 移动端响应式布局正常
- [ ] 批量选择功能正常工作
- [ ] 筛选和分类切换功能正常
- [ ] 已读/未读状态管理正常
- [ ] 分页功能正常工作

请完整实现这个页面，确保每个组件都符合开发规范要求，并实现完整的通知管理功能。

----------------------------------------------------------------------------------------------------------------------------

Overview

    Create a comprehensive quality inspection session detail page that supports multiple viewing modes (review, appeal processing, performance check, basic view) with audio playback,
     transcript highlighting, and interactive scoring features.

    Directory Structure

    src/app/(main)/demo/quality-inspection-detail/
    ├── _components/
    │   ├── page-header.tsx              # Page navigation and mode indicator
    │   ├── task-info-panel.tsx          # Task configuration and status
    │   ├── call-info-panel.tsx          # Basic call information
    │   ├── audio-player.tsx             # Audio controls with sync
    │   ├── transcript-panel.tsx         # Interactive text display
    │   ├── score-evolution-panel.tsx    # Visual score progression
    │   ├── scoring-details.tsx          # Rule display and editing
    │   ├── review-appeal-history.tsx    # Timeline and process records
    │   ├── operation-panel.tsx          # Mode-specific actions
    │   └── [utility-components]/        # Shared sub-components
    ├── page.tsx                         # Main page entry
    └── types.ts                         # TypeScript interfaces

    Implementation Steps

    Phase 1: Setup and Types (Day 1)

    1. Create directory structure
    2. Define TypeScript interfaces for session data, rules, and view modes
    3. Create mock data matching business requirements
    4. Set up URL parameter handling for view modes

    Phase 2: Core Components (Days 2-3)

    1. page-header.tsx: Navigation with back button and mode indicators
    2. task-info-panel.tsx: Task metadata display with responsive card layout
    3. call-info-panel.tsx: Agent/customer info with formatted display

    Phase 3: Audio & Transcript (Days 4-5)

    1. audio-player.tsx: Full controls with progress sync
    2. transcript-panel.tsx: Interactive text with highlighting and time jumps
    3. Implement audio-text synchronization logic

    Phase 4: Scoring System (Days 6-7)

    1. score-evolution-panel.tsx: Visual timeline of score changes
    2. scoring-details.tsx: Rule display with edit capabilities for review mode
    3. review-appeal-history.tsx: Process timeline with status indicators

    Phase 5: Mode-Specific Panels (Days 8-9)

    1. operation-panel.tsx: Dynamic panel based on viewMode
      - ReviewPanel: For quality reviewers
      - AppealPanel: For appeal processors
      - PerformanceCheckPanel: For agents
      - BasicViewPanel: For read-only access

    Phase 6: Integration and Polish (Day 10)

    1. Combine all components in page.tsx
    2. Implement responsive design (mobile-first)
    3. Add animations and transitions
    4. Test all view modes and interactions
    5. Verify ESLint and formatting compliance

    Key Features to Implement

    - Multi-mode support: URL parameter viewMode controls UI
    - Audio sync: Real-time transcript highlighting with audio
    - Score calculation: Dynamic score updates based on rule changes
    - Responsive design: Mobile-optimized layouts
    - Dark mode: CSS variable support throughout

    Mock Data Requirements

    - Realistic session data with full transcript
    - Multiple scoring rules with hit/miss states
    - Complete review and appeal history
    - Various view mode scenarios

    Testing Checklist

    - All view modes render correctly
    - Audio controls work smoothly
    - Transcript highlighting syncs with audio
    - Score calculations update in real-time
    - Responsive design works on mobile/tablet/desktop
    - Dark mode compatibility verified
    - ESLint and Prettier checks pass
    - All components under 100 lines (except page.tsx)

    Technical Notes

    - Use existing shadcn/ui components exclusively
    - Implement CSS variables for theme consistency
    - Follow kebab-case naming for all files
    - Use TypeScript strict mode throughout
    - Implement proper error boundaries

⏺ strut
  ⎿  Read business/FinalMultiModeSessionDetailPage.tsx (3092 lines)
  ⎿  Read business/话质检会话详情页面.md (593 lines)
  ⎿  Read package.json (86 lines)

----------------------------------------------------------------------------------------------------------------------------



----------------------------------------------------------------------------------------------------------------------------



----------------------------------------------------------------------------------------------------------------------------



----------------------------------------------------------------------------------------------------------------------------



----------------------------------------------------------------------------------------------------------------------------



----------------------------------------------------------------------------------------------------------------------------



----------------------------------------------------------------------------------------------------------------------------



----------------------------------------------------------------------------------------------------------------------------