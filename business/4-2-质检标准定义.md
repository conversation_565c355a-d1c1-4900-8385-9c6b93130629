# 4.2. 质检标准定义

## 4.2.1. 词库管理

### 功能目标
* **为质检策略师或主管提供一个集中管理关键词和短语集合的“字典”中心。**
* **将关键词汇与具体的规则逻辑解耦，实现关键词的统一维护和高效复用。**
* **支持对词库和词条的便捷创建、修改、导入导出，提升配置效率。**

### 目标用户
* 主要用户：质检主管、质检策略分析师。

### 核心功能与界面描述
* **词库列表页**
  * 页面布局：标准“头部+筛选器+列表+分页”
  * 页面元素：
    * 页面标题：“词库管理”
    * 核心操作按钮：
      * [主要] 新建词库：弹出创建新词库的模态框（Modal）
      * [次要] 导入词库：支持从外部文件（如JSON, CSV）批量导入词库及词条
    * 搜索筛选器：
      * 词库名称/描述：按关键字模糊搜索
      * 状态：下拉选择（已启用、已禁用）
    * 词库列表（Table）：
      * 列定义：
        * 序号
        * 词库信息：名称及状态徽章，下方为简要描述
        * 词条数量
        * 创建人 / 创建时间
        * 操作：
          * [管理词条]：弹出管理词条的模态框
          * [编辑]：修改词库名称和描述
          * [启用/禁用]：切换可用状态
          * [导出]：导出词库及词条
          * [删除]：删除词库
* **新建/编辑词库模态框**
  * 表单字段：
    * 词库名称（必填）
    * 词库描述（可选）
  * 操作按钮：保存、取消
* **词条管理模态框**
  * 模态框标题：当前词库名称 - 词条管理
  * 操作栏：
    * 新建词条
    * 导入词条
    * 导出词条
    * 搜索框
  * 词条列表（Table）：
    * 列定义：序号、词条内容、创建时间、更新时间、操作（编辑/删除）
  * 分页：词条多时分页

### 业务规则
* 词库名称在系统内必须唯一
* 词条在同一词库内必须唯一
* 被“规则”引用的词库删除需阻止或强风险提示
* 启用/禁用状态管理：被禁用词库引用时自动失效或按预设逻辑处理，确保系统稳定

### 与其他模块的关联
* 规则库管理：创建/编辑质检规则时，“关键词检查”算子下拉选择已启用词库

### 设计价值
* **解耦与复用**：词汇集合与规则逻辑分离，调整关键词只需改词库，所有引用规则自动更新，降低维护成本和风险
* **批量操作**：支持批量导入导出，便于与外部系统数据交换，提升初始化和迁移效率
* **两级管理结构**：清晰的“词库-词条”两级管理，逻辑清晰，交互流畅

## 4.2.2. 规则库管理

### 功能目标
* **为质检策略师或主管提供原子能力配置中心，创建、管理和维护独立可复用的质检规则。**
* **支持多种技术算子（关键词、正则表达式、LLM等）灵活定义判断逻辑，适应多样质检需求。**
* **引入自然语言创编规则，降低非技术人员门槛，提升配置效率和灵活性。**
* **对所有规则进行生命周期管理（创建、编辑、启用/禁用、删除）。**

### 目标用户
* 主要用户：质检主管、质检策略分析师。

### 核心功能与界面描述
* **规则列表页**
  * 页面布局：标准“头部+筛选器+列表+分页”
  * 页面元素：
    * 页面标题：“规则库管理”
    * 核心操作按钮：
      * [主要] 创建规则：抽屉（Drawer）弹出创建表单
    * 搜索筛选器：
      * 规则名称、重要程度、规则类型、规则算子、状态
    * 规则列表（Table）：
      * 列定义：
        * 序号
        * 规则名称：名称+重要程度徽章，下方简要描述
        * 规则类型
        * 规则算子：多标签展示技术类型
        * 创建人 / 最后修改时间
        * 操作：
          * [编辑]：弹出抽屉表单
          * [启用/禁用]：切换激活状态
          * [删除]：永久删除
* **创建/编辑规则抽屉表单**
  * 方式一：标准表单
    * 基本信息区：
      * 规则名称（必填）、规则描述
      * 规则类型（业务分类下拉）
      * 重要程度（违规等级下拉）
    * 条件配置区：
      * 逻辑关系：AND/OR 单选切换
      * 条件列表：动态增删，每行为“算子类型+配置项”
        * 选择算子后动态渲染配置项，如“关键词检查”需选择检测角色、检测范围、词库、分析方式等
    * 操作按钮：保存、取消
  * 方式二（可选）：Pro版表单-自然语言创编
    * 功能目标：引入LLM，用户用自然语言描述规则，系统解析为结构化条件
    * 界面：文本输入框+引导提示
    * 交互流程：
      1. 用户输入自然语言描述
      2. 点击“解析”或“生成规则”
      3. 系统调用LLM服务，解析为结构化条件并自动填充到标准表单
      4. 用户可微调确认后保存
    * 设计价值：极大降低非技术人员创建复杂规则门槛，体现系统智能化

### 业务规则
* 规则名称在系统内必须唯一
* 被“质检方案”引用的规则删除需阻止或强风险提示
* 启用/禁用状态：被禁用规则不会出现在方案可选列表，已包含的方案执行时自动跳过
* 算子库可扩展，便于未来增加新检测能力
* 系统内置算子库包括：关键词检查、正则表达式、通话静音检查、语速检查、抢话&打断、录音时长检测、非正常挂机、非正常接听、语句数检测、角色判断、能量检测、大模型检查（可选）

### 与其他模块的关联
* 词库管理：为“关键词检查”算子提供数据来源
* 大语言模型管理：为Pro版表单和大模型检查算子提供技术基础
* 质检方案配置：本模块为“质检方案”直接上游，所有“已启用”规则可组装为评分方案

### 成功标准
* 用户可通过标准表单成功创建基于“关键词检查”的规则
* 用户可通过Pro版表单用自然语言创建规则并正确解析条件
* 创建规则可被“质检方案配置”模块正确引用

## 4.2.3. 质检方案配置

### 功能目标
* **为管理者提供“装配中心”，将独立规则组合成完整带计分体系的质检方案。**
* **支持为每个规则分配分值，实现定性到定量的转化。**
* **对所有质检方案进行生命周期管理，便于后续任务直接调用。**

### 目标用户
* 主要用户：质检主管、质检策略分析师。

### 核心功能与界面描述
* **质检方案列表页**
  * 页面布局：标准“头部+筛选器+列表+分页”
  * 页面元素：
    * 页面标题：“质检方案管理”
    * 核心操作按钮：
      * [主要] 新建方案：抽屉弹出创建表单
      * [次要] 导入方案：支持从文件导入预设方案
    * 搜索筛选器：
      * 方案名称、状态
    * 方案列表（Table）：
      * 列定义：
        * 序号
        * 方案名称：名称+简要描述
        * 规则数
        * 通过分/总分：展示基准总分和合格分数线
        * 创建人 / 最后修改时间
        * 操作：
          * [编辑]：弹出抽屉表单
          * [启用/禁用]：切换可用状态
          * [删除]：永久删除
* **创建/编辑方案抽屉表单**
  * 第一部分：基本信息
    * 方案名称（必填）、方案描述
    * 基准总分（通常100）
    * 合格分数线
    * 申诉期限（天）
  * 第二部分：规则配置
    * [添加规则] 按钮：弹出规则选择器模态框
    * 规则选择器：列表展示所有“已启用”且未被当前方案添加的规则，支持搜索、筛选、多选
    * 已添加规则列表：列表/卡片展示，
      * 每条规则后有输入框设置扣分值（负数或0）
      * 每条规则可删除
    * 最终分数预览：实时显示“基准总分+所有扣分值总和”
  * 抽屉底部操作栏：保存、取消

### 业务规则
* 方案名称在系统内必须唯一
* 扣分值应≤0
* 被“质检任务/计划”引用的方案删除需阻止或强风险提示

### 与其他模块的关联
* 规则库管理：本模块消费“规则库”中创建的规则
* 质检计划管理&质检任务管理：本模块为其直接上游，创建任务时需选择“已启用”方案

### 设计价值
* **组合与计分**：连接“定性规则”与“定量评估”，完成标准到评分体系的构建
* **场景化封装**：通过不同方案满足多业务场景需求，提升精细化和针对性
* **生命周期管理**：完整的创建、编辑、启用/禁用、删除生命周期，保证配置灵活性和可维护性 