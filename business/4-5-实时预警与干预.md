# 4.5. 实时预警与干预

## 4.5.1. 实时预警中心

### 功能目标
* **为质检主管和班组长提供“作战指挥室”，实时暴露高风险通话事件。**
* **将风险感知转化为可操作的预警信号。**
* **提供事件严重性上下文，支持分级响应和状态流转。**
* **实现从被动等待报告到主动发现并干预问题的转变。**

### 目标用户
* 主要用户：质检主管、班组长。

### 核心功能与界面描述
* **页面布局与导航**
  * 布局："头部+核心指标卡+信息流"，强调实时性和动态性
  * 页面标题：“实时预警中心”
  * 副标题：“实时监控高风险通话，实现主动式服务质量管理与干预”
  * 页面图标：Siren
  * 核心操作：
    * [预警流设置]：弹出框配置参数
    * [接收预警开关]：全局开关，临时暂停/恢复接收预警
* **核心实时指标 (KPI Cards)**
  * 今日预警总数：累计触发数，带趋势
  * 待处理预警：未读/处理中数量，进度条展示
  * 最高风险等级：当前待处理中的最高等级
  * 实时通话并发数：当前系统处理通话总数
* **实时预警流 (Live Alert Feed)**
  * 信息流按时间倒序展示所有预警事件，支持手动刷新
  * 视图切换(Tab)：
    * 新预警（默认）：未读+处理中，Tab角标显示数量
    * 已处置：已读+已解决，便于回顾
  * 单条预警卡片：
    * 时间戳
    * 风险等级（颜色条+标签）
    * 触发规则
    * 通话信息（坐席、班组、客户）
    * 上下文摘要（关键词高亮）
    * 实时操作按钮：
      1. 标记已读
      2. 标记处理中
      3. 创建跟进任务
      4. 实时监听（高级）
      5. 查看详情
* **预警规则热力图 (Heatmap/Ranking List)**
  * 右侧/下方排行榜，展示Top 5触发规则及次数占比
  * 价值：宏观发现风险点

### 核心交互与工作流
1. 被动接收与发现：进入页面加载预警流，高风险主动呈现
2. 快速评估：通过风险等级和上下文摘要快速判断严重性
3. 分级响应与状态流转：
  * 低风险 -> 标记已读
  * 中风险 -> 标记处理中
  * 高风险 -> 创建跟进任务，移交[预警跟进中心]
4. 趋势分析：通过热力图发现问题规律

### 成功标准
* 关键词命中时1分钟内刷新预警
* 管理者可通过卡片快速了解核心信息
* 高风险预警可顺利转为可追踪跟进任务

## 创建跟进任务 与 预警详情抽屉

### 功能目标
* **提供沉浸式视图，聚合单次预警事件所有相关信息。**
* **聚合预警、通话、后续任务，提供完整追溯入口。**
* **根据场景动态展示操作选项。**

### 组件设计与功能模块
* 头部区域：
  * 标题+唯一预警ID
  * 状态徽章
  * 关闭按钮
* 主体内容区：
  * 预警核心信息：等级、触发时间、规则（悬浮可查描述）
  * 事发上下文：
    * 关联通话ID（可跳转多模式详情页）
    * 坐席/班组/客户信息
    * 上下文摘要：对话形式展示，角色图标、时间戳、播放按钮、内容高亮
  * 处置与跟进记录：
    * 处置状态、处置人、处置时间
    * 关联跟进任务卡片（如有）：标题、状态、负责人、截止日期、查看详情
* 底部操作栏：
  * 仅实时预警中心"新预警"时显示
  * 按钮：标记已读、标记处理中、创建跟进任务

### 设计哲学
* 信息聚合：预警、通话、任务三者聚合，完整追溯
* 上下文保留：抽屉/模态框不脱离主列表
* 分层下钻：摘要->聚合详情->原始场景
* 组件复用：通过props控制，服务多场景

### 创建跟进任务交互
* 弹出模态框/抽屉，专注任务快速创建
* 自动填充：预警ID、通话ID、核心信息
* 用户填写：
  * 任务标题（必填，默认可编辑）
  * 任务描述（可选）
  * 指派给（必填，下拉/搜索，默认当前用户）
  * 优先级（必填）
  * 截止日期（可选）
* 操作按钮：创建任务、取消
* 后续流程：
  1. 创建新任务，预警状态自动"已解决"
  2. 通知被指派人
* 设计价值：高效流转、职责明确、上下文保留

## 4.5.2. 预警跟进中心

### 功能目标
* **为管理者提供结构化平台，追踪、处理、复盘所有跟进任务。**
* **作为高风险事件后续处置管理中枢，确保每个预警闭环。**
* **将警报沉淀为可审计的管理行为记录。**

### 目标用户
* 主要用户：质检主管、班组长。

### 核心功能与界面描述
* **页面布局与导航**
  * 标准"头部+Tab切换+筛选器+列表+分页"
  * 页面标题："预警跟进中心"
  * 副标题："追踪和管理所有高风险预警的跟进任务与处置过程"
  * 页面图标：ClipboardList
* **Tab切换视图**
  * 待处理(todo)：显示所有待处理任务，Tab角标显示数量
  * 已完成(done)：显示所有已完成任务
* **统一搜索筛选器**
  * 任务标题/描述
  * 关联触发规则
  * 关联坐席/班组
  * 指派给
  * 优先级
  * 创建时间/截止日期范围
* **任务列表视图 (List View)**
  * 传统表格，信息密度高，支持排序
  * 列定义：
    * 序号
    * 任务标题
    * 状态
    * 优先级
    * 指派给
    * 关联坐席/班组
    * 创建时间
    * 截止日期
    * 操作："处理"按钮
  * 交互：点击"处理"右侧滑出详情抽屉
* **任务详情抽屉 (Task Detail Drawer)**
  * 单栏上下结构
  * 任务详情卡片：状态、优先级、负责人、关联坐席、截止日期、触发规则、描述
  * "查看关联通话详情"链接
  * 处理结果区域：
    * 结果输入框
    * 附件上传
    * 提交处理结果，任务状态变"已完成"

### 核心交互与工作流
1. 任务接收：被指派人收到新任务提醒或在待处理页看到
2. 任务处理：
  * 找到任务，点击"处理"打开详情
  * 回顾原始通话场景
  * 执行线下辅导或沟通
  * 填写处理结果并上传附件，提交后归档
3. 监督与复盘：主管可随时查看进展和结果
4. 闭环与沉淀：所有处理过程和结果完整保存，供复盘和分析

### 设计价值
* **闭环管理**：每个高风险事件都跟进、处理、有结果、有记录
* **过程沉淀**：管理行为线上化沉淀，便于培训和优化
* **职责明确**：指派和处理流程确保责任到人，避免推诿
