这是一个功能完整的通话质检会话详情页面组件，支持多种查看模式（质检复核、申诉处理、绩效检查、基础查看），整合了音频播放、文本展示、评分详
  情、流程追踪等核心功能。

  核心数据结构

  SessionData 接口

  - 基本信息：会话ID、坐席信息、客户信息、通话时间
  - 评分信息：机器评分、复核评分、申诉评分、最终评分
  - 处理状态：申诉状态（可申诉/进行中/已处理/已过期）
  - 扩展信息：任务详情、复核记录、申诉记录、触发条件

  评分规则系统

  - ScoringRule：包含规则ID、名称、描述、是否命中、扣分值
  - reviewedScoringRules：人工复核后的规则状态
  - ProcessTimeline：完整的质检处理时间轴

  主要功能模块

  1. 音频播放器 (AudioPlayer)

  - 完整的音频控制（播放/暂停/快进/快退）
  - 可视化进度条，支持点击跳转
  - 时间格式化显示
  - 播放状态实时指示

  2. 通话文本 (TranscriptPanel)

  - 实时高亮当前播放位置
  - 发言者区分（坐席/客户）
  - 事件标签：负面情绪、关键词、长时静默
  - 点击文本跳转到对应时间点

  3. 评分面板 (ScorePanel)

  - 分数演进流程：
    - AI初检 → 人工复核 → 申诉裁定
    - 可视化分数变化轨迹
    - 合格/不合格状态指示
  - 环形进度条：动态展示最终得分
  - 分数来源标识：清晰显示当前分数来源

  4. 评分详情 (ScoringDetailsPanel)

  - 规则展示：
    - 通过/未通过状态区分
    - 扣分详情和触发时间
    - 机审与人工复核结果对比
  - 交互功能：
    - 规则跳转（跳转到对应时间点）
    - 复核状态调整（人工复核模式）
    - 分数调整功能

  5. 处理时间轴 (ProcessTimeline)

  - 完整的质检流程记录
  - 可视化的处理进度
  - 每个环节的详细时间戳

  查看模式

  1. 基础查看模式 (basic_view)

  - 只读状态，展示基础信息
  - 完整的通话记录和评分
  - 无操作权限

  2. 质检复核模式 (review)

  - 人工复核功能
  - 可调整评分规则
  - 提交复核意见

  3. 申诉处理模式 (appeal_processing)

  - 处理坐席申诉
  - 支持同意/驳回申诉
  - 可调整最终分数

  4. 绩效检查模式 (performance_check)

  - 坐席查看自己的质检结果
  - 支持发起申诉
  - 申诉状态实时显示

  界面设计特点

  1. 渐变背景与卡片设计

  - 使用渐变色彩增强视觉层次
  - 卡片式布局，清晰分区
  - 毛玻璃效果提升现代感

  2. 动画交互

  - 页面切换平滑过渡
  - 悬停效果与微交互
  - 实时状态动画（播放指示器、进度条）

  3. 响应式设计

  - 三栏布局（大屏）→ 单栏布局（小屏）
  - 自适应卡片大小
  - 移动端友好的交互元素

  4. 信息层次

  - 重要信息优先展示
  - 颜色编码区分状态
  - 图标辅助理解

  关键交互流程

  质检复核流程

  1. 查看机器初检结果
  2. 逐项审核评分规则
  3. 调整不合理的扣分
  4. 填写复核意见
  5. 提交复核结果

  申诉处理流程

  1. 查看申诉理由
  2. 评估申诉合理性
  3. 选择处理决定（同意/驳回）
  4. 如同意，调整最终分数
  5. 填写处理意见
  6. 提交处理结果

  发起申诉流程

  1. 查看当前质检结果
  2. 点击"发起申诉"
  3. 填写申诉理由
  4. 提交申诉申请

  技术实现亮点

  1. 状态管理

  - 使用React hooks管理复杂状态
  - useMemo优化计算性能
  - 实时状态同步

  2. 组件化设计

  - 高度可复用的子组件
  - 清晰的props接口定义
  - 模块化功能封装

  3. 用户体验优化

  - 流畅的动画过渡
  - 直观的操作反馈
  - 清晰的信息架构

  4. 错误处理

  - 表单验证
  - 状态边界检查
  - 用户友好的错误提示

  使用场景

  - 质检专员：进行人工复核
  - 申诉处理员：处理坐席申诉
  - 坐席本人：查看质检结果并发起申诉
  - 管理层：查看完整的质检流程记录

----------------------------------------------------------------------------------------------------------------------------

一、整体布局结构

  1.1 页面框架

  ┌─────────────────────────────────────────────────────────────┐
  │ 顶部导航栏 (固定在顶部)                                      │
  │ ┌───────────────────────────────────────────────────────┐   │
  │ │ 返回按钮 | 会话详情标题 | 状态指示器                     │   │
  │ └───────────────────────────────────────────────────────┘   │
  └─────────────────────────────────────────────────────────────┘
  ┌─────────────────────────────────────────────────────────────┐
  │                                                             │
  │  主要内容区域 (三栏布局)                                    │
  │  ┌─────────────────────────────┬─────────────────────────┐  │
  │  │                             │                         │  │
  │  │ 左侧主内容区 (占2/3)        │ 右侧侧边栏 (占1/3)      │  │
  │  │ ┌─────────────────────────┐ │ ┌─────────────────────┐ │  │
  │  │ │ 任务信息卡片            │ │ │ 触发复核原因        │ │  │
  │  │ │ 通话信息卡片            │ │ │ 评分面板            │ │  │
  │  │ │ 音频播放器              │ │ │ 处理时间轴          │ │  │
  │  │ │ 通话文本                │ │ │ 评分详情            │ │  │
  │  │ └─────────────────────────┘ │ │ 复核详情            │ │  │
  │  │                             │ │ 申诉详情            │ │  │
  │  │                             │ │ 操作面板            │ │  │
  │  └─────────────────────────────┴─────────────────────────┘  │
  └─────────────────────────────────────────────────────────────┘

  1.2 响应式布局

  - 大屏 (>1200px): 三栏布局
  - 中屏 (768px-1200px): 两栏布局
  - 小屏 (<768px): 单栏垂直布局

  二、顶部导航栏 (Top Navigation Bar)

  2.1 位置与样式

  - 位置: 固定在页面顶部，z-index: 50
  - 背景: 白色半透明 + 毛玻璃效果
  - 边框: 底部1px灰色边框
  - 高度: 64px

  2.2 内容结构

  // 左侧返回区域
  ┌─────────────────────┐
  │ ← 返回列表          │  // 可点击返回上一页
  └─────────────────────┘

  // 中间标题区域
  ┌─────────────────────┐
  │ 📱 会话详情         │  // 固定标题
  └─────────────────────┘

  // 右侧状态区域
  ┌─────────────────────┐
  │ ● 实时状态指示器    │  // 蓝色脉冲点
  └─────────────────────┘

  2.3 交互逻辑

  - 返回按钮：点击触发 navigate(-1) 返回上一页
  - 悬停效果：按钮放大1.05倍，颜色从灰色变为蓝色

  三、左侧主内容区

  3.1 任务信息卡片 (TaskInfoPanel)

  3.1.1 布局结构

  ┌─────────────────────────────────────────┐
  │ 📋 任务信息                              │
  │ ┌─────────────────────────────────────┐ │
  │ │ 任务状态卡片                        │ │
  │ │ ┌─────────────┬───────────────────┐ │ │
  │ │ │ 任务状态    │ 处理进度条        │ │ │
  │ │ │ ✅ 已完成   │ ▓▓▓▓▓▓▓▓░░ 100%   │ │ │
  │ │ └─────────────┴───────────────────┘ │ │
  │ └─────────────────────────────────────┘ │
  │                                         │
  │ 核心信息网格 (3列)                      │
  │ ┌─────────────┬─────────────┬─────────┐ │
  │ │ 💯 评分标准 │ 👤 任务来源 │ 📅 时间 │ │
  │ │ 基准: 100   │ 创建人: 张 │ 创建... │ │
  │ │ 合格: 80    │ 来源: 手动 │ 截止... │ │
  │ └─────────────┴─────────────┴─────────┘ │
  └─────────────────────────────────────────┘

  3.1.2 详细内容

  - 任务状态区域:
    - 状态标签：彩色圆角标签，包含图标
    - 进度条：动态填充动画，颜色根据状态变化
  - 核心信息网格:
    - 评分标准：显示基准分和合格线
    - 任务来源：创建人、来源类型（手动/计划）
    - 时间信息：创建时间、截止时间

  3.2 通话信息卡片 (CallInfoPanel)

  3.2.1 布局结构

  ┌─────────────────────────────────────────┐
  │ 📱 通话信息                              │
  │ ┌─────────┬─────────┬─────────┬───────┐ │
  │ │ 👤 坐席 │ 🏢 班组 │ 📞 客户 │ ⏰ 时间│ │
  │ │ 王伟/A001│ A组     │138****1234│14:30│ │
  │ └─────────┴─────────┴─────────┴───────┘ │
  └─────────────────────────────────────────┘

  3.3 音频播放器 (AudioPlayer)

  3.3.1 布局结构

  ┌─────────────────────────────────────────┐
  │ 🎧 音频播放器                            │
  │                                         │
  │     ┌─────┐    ┌─────────────┐    ┌────┐│
  │     │ ⏮️  │    │      ▶️     │    │ ⏭️ ││  // 控制按钮
  │     └─────┘    └─────────────┘    └────┘│
  │                                         │
  │ 00:00 ━━━━━━━━━━━━━━━━━━━━ 03:35        │  // 进度条
  │                                         │
  │ 播放状态: 已暂停 ●                       │
  └─────────────────────────────────────────┘

  3.3.2 交互逻辑

  - 播放控制:
    - 播放/暂停：点击中心大按钮
    - 快退/快进：点击两侧按钮，每次±10秒
    - 进度条：点击任意位置跳转
  - 状态同步:
    - 播放时：按钮变为暂停图标，状态文字更新
    - 暂停时：按钮变为播放图标

  3.4 通话文本 (TranscriptPanel)

  3.4.1 布局结构

  ┌─────────────────────────────────────────┐
  │ 💬 通话文本                              │
  │ ┌─────────────────────────────────────┐ │
  │ │ 头部统计                            │ │  // 消息数量、当前时间
  │ └─────────────────────────────────────┘ │
  │                                         │
  │ ┌─────────────────────────────────────┐ │
  │ │ 💬 坐席 00:05                       │ │  // 每条消息
  │ │ 您好，这里是xx银行...               │ │
  │ │ 😡 负面情绪                          │ │  // 事件标签
  │ └─────────────────────────────────────┘ │
  │                                         │
  │ ┌─────────────────────────────────────┐ │
  │ │ 👤 客户 00:15                       │ │
  │ │ 是我，你们怎么又打电话来了...       │ │
  │ └─────────────────────────────────────┘ │
  └─────────────────────────────────────────┘

  3.4.2 消息卡片详细结构

  每个消息卡片包含：
  - 头像区域: 圆形头像，显示坐席/客户图标
  - 时间戳: 精确到秒的时间显示
  - 发言者标签: "坐席"或"客户"
  - 文本内容: 完整的对话内容
  - 事件标签: 负面情绪、关键词、长时静默等
  - 播放指示器: 当前播放位置的蓝色高亮

  3.4.3 交互逻辑

  - 点击跳转: 点击任意消息跳转到对应时间点
  - 悬停效果: 卡片轻微放大，背景变色
  - 实时高亮: 当前播放位置的消息高亮显示

  四、右侧侧边栏

  4.1 触发复核原因 (ReviewTriggerPanel)

  4.1.1 适用场景

  - 仅在 review 模式下显示
  - 当有触发条件时自动展示

  4.1.2 布局结构

  ┌─────────────────────────────────────────┐
  │ 🚩 触发复核原因                          │
  │ ┌─────────────────────────────────────┐ │
  │ │ ⚠️ 随机抽样复核                      │ │
  │ └─────────────────────────────────────┘ │
  │ ┌─────────────────────────────────────┐ │
  │ │ ⚠️ 低分触发复核                      │ │
  │ └─────────────────────────────────────┘ │
  └─────────────────────────────────────────┘

  4.2 评分面板 (ScorePanel)

  4.2.1 布局结构

  ┌─────────────────────────────────────────┐
  │ 🎯 评分面板                              │
  │                                         │
  │     ┌─────────┐                         │
  │     │   90    │      合格               │  // 环形进度条
  │     │   分    │      ✅ 合格            │
  │     └─────────┘      📋 申诉裁定        │
  │                                         │
  │ AI初检 → 人工复核 → 申诉裁定             │  // 分数演进流程
  │   72      80        90                  │
  │                                         │
  │ 最终分数来源：申诉裁定                   │
  └─────────────────────────────────────────┘

  4.2.2 环形进度条实现

  - SVG实现: 使用SVG绘制环形进度
  - 动画效果: 从0到目标分数的填充动画
  - 颜色编码: 绿色(合格)/红色(不合格)
  - 状态图标: 合格显示✅，不合格显示⚠️

  4.2.3 分数演进流程

  - 三阶段显示: AI初检、人工复核、申诉裁定
  - 连接线动画: 从左到右的填充动画
  - 当前状态标识: 高亮显示当前有效的分数来源

  4.3 处理时间轴 (ProcessTimeline)

  4.3.1 适用场景

  - 非review模式显示
  - 展示完整的质检流程

  4.3.2 布局结构

  ┌─────────────────────────────────────────┐
  │ ⏰ 处理时间轴                            │
  │                                         │
  │ 1 ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ │  // AI初检
  │   AI初检完成                            │
  │   2023-10-27 14:30:05                   │
  │                                         │
  │ 2 ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ │  // 人工复核
  │   人工复核                              │
  │   李经理 2023-10-27 16:10:25           │
  │                                         │
  │ 3 ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ │  // 申诉发起
  │   申诉发起                              │
  │   王伟 2023-10-28 09:30:15             │
  └─────────────────────────────────────────┘

  4.4 评分详情 (ScoringDetailsPanel)

  4.4.1 布局结构

  ┌─────────────────────────────────────────┐
  │ 📋 评分详情                              │
  │ ┌─────────────────────────────────────┐ │
  │ │ ✅ 标准开场白检测                    │ │  // 通过的规则
  │ │ 检测坐席开场白是否符合标准句式       │ │
  │ │ +0 已通过                            │ │
  │ └─────────────────────────────────────┘ │
  │ ┌─────────────────────────────────────┐ │
  │ │ ❌ 上下文重复询问                    │ │  // 扣分的规则
  │ │ 检测坐席是否重复询问相同问题         │ │
  │ │ -10 已扣分 00:28                    │ │
  │ └─────────────────────────────────────┘ │
  └─────────────────────────────────────────┘

  4.4.2 规则卡片详细结构

  每个规则卡片包含：
  - 状态图标: ✅通过/❌未通过
  - 规则名称: 加粗显示
  - 规则描述: 灰色小号文字
  - 扣分值: 红色显示扣分，绿色显示+0
  - 触发时间: 格式为mm:ss
  - 跳转按钮: 可跳转到对应时间点

  4.5 操作面板 (动态显示)

  4.5.1 质检复核模式 (ReviewPanel)

  ┌─────────────────────────────────────────┐
  │ 👁️ 复核操作                              │
  │                                         │
  │ ┌─────────────┬─────────────┐           │
  │ │ AI初检: 72  │ 复核后: 80  │           │  // 分数对比
  │ └─────────────┴─────────────┘           │
  │                                         │
  │ 复核意见:                                │
  │ ┌─────────────────────────────────────┐ │
  │ │ [文本输入框]                        │ │
  │ └─────────────────────────────────────┘ │
  │                                         │
  │ [取消复核] [提交复核结果]                │
  └─────────────────────────────────────────┘

  4.5.2 申诉处理模式 (AppealPanel)

  ┌─────────────────────────────────────────┐
  │ 🛡️ 申诉处理                              │
  │                                         │
  │ 申诉理由:                                │
  │ "客户实际已经表达了满意的意愿..."        │
  │                                         │
  │ 处理决定:                                │
  │ ┌───────────────┬─────────────────┐     │
  │ │ ✅ 同意申诉   │ ❌ 驳回申诉      │     │
  │ └───────────────┴─────────────────┘     │
  │                                         │
  │ 调整后分数: [输入框]                    │
  │                                         │
  │ 处理意见:                                │
  │ ┌─────────────────────────────────────┐ │
  │ │ [文本输入框]                        │ │
  │ └─────────────────────────────────────┘ │
  └─────────────────────────────────────────┘

  4.5.3 绩效检查模式 (PerformanceCheckPanel)

  ┌─────────────────────────────────────────┐
  │ ⏰ 申诉操作                              │
  │                                         │
  │ 当前状态: 可申诉                        │
  │ 申诉截止: 2023-10-30 18:00:00          │
  │                                         │
  │ [立即发起申诉 →]                        │
  └─────────────────────────────────────────┘

  五、状态管理与数据流

  5.1 全局状态

  // 会话数据
  session: SessionData = {
    id: 'S_A001_01',
    agent: { id: 'A001', name: '王伟', teamName: 'A组' },
    customer: { id: 'C_12345', phone: '138****1234' },
    startTime: '2023-10-27 14:30:05',
    duration: 215,
    machineScore: 72,
    reviewScore: 80,
    appealScore: 90,
    finalScore: 90,
    finalScoreSource: 'appeal',
    // ... 其他字段
  }

  // 音频播放状态
  currentTime: number = 0
  isPlaying: boolean = false

  // 复核状态
  reviewedScoringRules: ScoringRule[] = [...]
  calculatedReviewScore: number = 80

  5.2 状态更新机制

  1. 音频播放: 每秒更新 currentTime
  2. 规则调整: 实时更新 calculatedReviewScore
  3. 模式切换: 通过 URL 参数 viewMode 控制
  4. 数据提交: 表单提交后更新会话状态

  5.3 计算属性

  - calculatedReviewScore: 基于当前规则实时计算复核分数
  - displayedRules: 根据查看模式显示不同的规则集
  - scorePanelProps: 动态生成评分面板所需的属性

  六、交互逻辑详解

  6.1 音频播放交互

  // 播放控制
  isPlaying = true → 启动定时器 → 每秒 currentTime + 1
  isPlaying = false → 清除定时器

  // 进度条点击
  handleProgressClick(e) → 计算点击位置 → setCurrentTime(newTime)

  // 文本跳转
  点击消息 → setCurrentTime(message.timestamp)

  6.2 质检复核交互

  // 规则调整
  点击规则 → 切换通过/未通过状态 → 重新计算分数
  调整分数 → 更新规则分数 → 重新计算分数

  // 提交复核
  填写复核意见 → 点击提交 → 更新会话状态 → 返回列表页

  6.3 申诉处理交互

  // 处理申诉
  选择决定 → 如同意则输入新分数 → 填写处理意见 → 提交

  // 发起申诉
  点击发起申诉 → 填写申诉理由 → 提交 → 状态变为"申诉中"

  七、动画与过渡效果

  7.1 页面加载动画

  - 整体淡入: opacity: 0 → 1
  - 元素依次进入: 每个区块延迟0.1-0.8秒
  - 缩放效果: scale: 0.8 → 1

  7.2 交互反馈动画

  - 按钮悬停: scale: 1 → 1.05
  - 卡片悬停: scale: 1 → 1.02
  - 进度条填充: width: 0 → 100%

  7.3 状态切换动画

  - 播放/暂停: 图标旋转切换
  - 消息高亮: 背景色渐变
  - 分数变化: 数字滚动效果