# 4.6. 数据洞察与分析

如果说AI全量质检解决了数据采集的"广度"问题，人工复核与申诉解决了数据"准度"问题，那么本章节所阐述的**数据洞察与分析**模块，则旨在解决数据价值实现的"深度"问题。它将海量的、分散的、微观的质检数据，聚合成宏观的、多维的、可下钻的分析视图，为各层级管理者提供科学决策的依据，是整个系统**数据驱动**设计哲学的集中体现。

---

## 4.6.1. 质检运营总览

### 功能目标
* **为最高层管理者（如质检主管、运营总监）提供一个宏观的、一览无余的驾驶舱。**
* **用于快速、直观地监控整个质检体系的运营健康度、AI运行效率和核心风险。**
* **帮助管理者快速把握整体态势，并发现需要进一步下钻分析的领域。**
* **作为所有分析报表的顶层入口，提供决策所需的最核心数据概览。**

### 目标用户
* 主要用户：质检主管、运营总监、公司管理层。

### 核心功能与界面描述
* **页面布局与导航**
  * 页面布局：采用典型的Dashboard布局，由"全局筛选器+KPI指标卡+趋势图+排行榜"构成。
  * 页面标题："质检运营总览"。
  * 副标题："从最高维度宏观监控整体质检工作的健康度、AI运行效率和核心风险..."。
  * 核心操作：刷新数据 和 导出报表（可将当前页面视图导出为PDF或图片）。
* **统一搜索筛选器**
  * 目的：允许管理者对整个报表的数据范围进行筛选，以进行更具针对性的分析。
  * 筛选字段：
    * 时间范围：最重要的筛选条件，用于定义报表的统计周期（如本周、本月、上季度、自定义范围）。
    * 班组/团队：可以下钻到特定团队或一组团队进行分析。
    * 质检方案：可以筛选应用了特定方案的数据，用于评估该方案的效果。
* **核心KPI指标卡片 (KPICard 网格布局)**
  * 在页面顶部，以最直观的方式展示整个质检体系的全局核心指标。
  * 指标内容：
    * 总质检量：AI处理的通话总数，反映系统覆盖面和效率。
    * 总复核量：人工审核的总数，反映人工成本和投入。
    * 复核率：人工审核的比例，是平衡成本与质量的关键指标。
    * 整体平均分：全局服务质量的基准线。
    * 整体合格率：全局服务质量的达标情况。
    * 整体申诉率：全局质检结果的公信力指标。
* **运营趋势分析（组合图表）**
  * 图表标题："质量与效率趋势分析"。
  * 内容：使用一个组合图表 (Composed Chart)，将两个不同量纲的核心指标放在一张图上进行对比分析：
    1. 总质检量 (Bar Chart - 柱状图)：以柱状图形式展示每日或每周的质检量，直观反映工作负荷的波动。
    2. 整体平均分 (Line Chart - 折线图)：以折线图形式叠加在柱状图之上，展示同期服务质量得分的趋势。
  * 价值：管理者可以非常直观地分析"质检量"与"质量得分"之间的关系。例如，是否在业务高峰期（质检量大）时，服务质量（平均分）有所下滑？这为资源调配和风险预警提供了依据。
* **问题风险聚焦（三列卡片布局）**
  * 该模块旨在帮助管理者快速定位最突出的问题和表现最佳/最差的单位。
  * 左侧卡片 - 高频失分规则 TOP 10 (RankingCard):
    * 内容：列出在选定时间范围内，导致全公司失分次数最多的10条规则。
    * 价值：直接指明了当前服务流程或员工培训中最普遍的短板。
  * 中间卡片 - 严重错误规则 TOP 10 (RankingCard):
    * 内容：列出在全公司范围内，被触发次数最多的"严重违规"级别规则。
    * 价值：暴露了公司当前面临的核心合规风险或服务红线问题。
  * 右侧卡片 - 平均分排名 (RankingCard with Toggle):
    * 内容：这是一个可切换的排名榜。
      * 默认显示团队排名：按平均分对所有团队进行排序。
      * 提供"团队/坐席"切换按钮，点击后可切换为显示坐席排名：在所有坐席中按平均分进行排序。
    * 价值：快速识别出表现优异和有待提升的团队/个人，为绩效评估、标杆树立和资源倾斜提供参考。
* **核心交互**
  * 宏观洞察：页面的所有图表和数据都是全局性的，旨在提供"一览无余"的顶层视图。
  * 数据筛选与下钻：通过顶部的筛选器，管理者可以将宏观视图聚焦到特定的业务线或团队，进行初步的下钻分析。
  * 问题发现：页面的设计思路是问题导向，通过KPI、趋势和排行榜，主动将最值得关注的"亮点"和"痛点"暴露给管理者。
  * 决策支持：该页面是管理层制定季度/月度目标、评估质检工作成效、调整运营策略的核心数据来源。

---

## 4.6.2. 服务质量深度分析

### 功能目标
* **提供一个专题分析报表，让管理者能够围绕任意一个具体的"质检规则"进行深度下钻分析。**
* **旨在回答"某个特定的服务质量问题（例如'客户不满未道歉'）在我们的组织中表现如何？根源在哪里？"这类具体问题。**
* **为精准的培训、针对性的辅导以及规则本身的优化提供直接、具体的数据依据。**

### 目标用户
* 主要用户：质检主管、班组长。

### 核心功能与界面描述
* **页面布局与导航**
  * 页面布局：采用"全局筛选器 + 规则总览表 + 动态下钻分析区"的布局。
  * 页面标题："服务质量深度分析"。
  * 副标题："下钻到具体的质检规则维度，分析服务质量的短板，并定位到具体的团队和个人..."。
  * 页面图标：Target（靶心图标），象征着精准定位和深度分析。
* **统一搜索筛选器**
  * 与总览页面类似，用于定义分析的数据范围。
  * 筛选字段：时间范围、质检方案、班组/团队、坐席。
* **规则表现总览表格 (RulePerformanceTable)**
  * 功能：这是页面的起点和入口。它以表格形式列出了在筛选范围内所有被触发过的质检规则，并展示了每条规则的综合表现。
  * 表格列定义：
    * 规则名称：附带其"严重程度"徽章。
    * 规则类型：业务分类。
    * 平均得分率：核心指标。指所有与该规则相关的通话，在该规则项上的平均得分率（例如，满分-5分，实际扣-1分，则得分率80%）。它比简单的触发次数更能反映问题的严重程度。
    * 触发次数：该规则被命中的总次数。
    * 申诉次数：针对该规则的申诉数量。
    * 申诉成功率：针对该规则的申诉成功比例。高成功率可能意味着规则本身或AI判断存在问题。
    * 操作：提供一个 "下钻分析" 的链接/按钮。
  * 交互：
    * 用户首先在此表格中选择一个他感兴趣或表现异常的规则（例如，平均得分率最低的规则），然后点击"下钻分析"。
    * 点击后，该行会高亮，并且下方会动态展开一个详细的分析区域 (RuleDrillDownAnalysis)。
* **规则下钻分析区域 (RuleDrillDownAnalysis - 动态展开)**
  * 这是页面的核心内容，只有在用户点击了某条规则后才会出现。它包含针对所选定规则的深入分析图表和列表。
  * 趋势分析 (左右双图表布局)：
    * 左侧图表 - 规则得分趋势 (LineChart): 展示在选定时间范围内，该规则的每日/周平均得分率的变化趋势。用于判断该问题是持续存在、正在改善还是正在恶化。
    * 右侧图表 - 触发次数趋势 (BarChart): 展示该规则的每日/周触发次数的变化趋势。可以与得分率趋势结合分析，例如，触发次数下降但得分率没变，可能说明问题仍未解决，只是发生频率降低。
  * 表现排名 (左右双列表布局)：
    * 左侧列表 - 团队表现排名: 按团队对该规则的平均得分率进行排名，快速定位哪个团队在该问题上做得最好（可作为标杆），哪个团队最差（需要重点辅导）。
    * 右侧列表 - 坐席表现排名: 按坐席对该规则的平均得分率进行排名，直接定位到具体的个人。
  * 典型问题录音 (ProblemCase 列表)：
    * 功能：系统会自动筛选出几条因为该规则而被严重扣分的典型通话案例。
    * 内容：每条案例会显示记录ID、坐席、分数、通话信息，以及问题描述（例如，"客户在3分15秒表达不满，坐席未道歉"）。
    * 交互：提供"播放录音"按钮，让管理者可以身临其境地感受问题发生的具体场景。
  * 成员表现分布图 (PerformanceDistributionChart) (可选辅助图表):
    * 以柱状图形式，展示在筛选范围内，所有成员的最终得分分布情况（例如，90-100分有多少人，80-89分有多少人等）。
    * 帮助管理者了解团队整体表现的正态分布情况，是"纺锤形"（大部分人居中）还是"哑铃型"（两极分化严重）。
* **核心交互与价值**
  * 逐层下钻：页面的核心交互是"总览 -> 选择 -> 下钻"。用户从规则总览表格中发现问题线索，然后点击进入针对该问题的深度分析，再从团队排名下钻到个人，最后通过典型案例触达问题的原始场景。这是一个非常强大和高效的分析路径。
  * 精准定位：整个页面的设计都是为了帮助管理者精准定位问题：不仅知道"是什么问题"（规则本身），还知道"问题有多严重"（得分率和趋势），以及"问题出在谁身上"（团队和个人排名）。
  * 场景还原："典型问题录音"功能是点睛之笔，它将抽象的数据与具体的服务场景联系起来，让分析和辅导更有说服力。

---

## 4.6.3. 复核工作分析报告

### 功能目标
* **提供一个专注于人工复核环节的专题分析报表。**
* **全面评估和监控复核团队的工作状态和质量，包括工作量、效率和准确性。**
* **深入分析AI初检结果与人工复核结果之间的差异，这是衡量AI准确率、发现AI短板、驱动AI模型优化的核心依据。**
* **帮助管理者统一复核员之间的打分尺度，提升整个复核团队的专业性和一致性。**

### 目标用户
* 主要用户：质检主管。
* 次要用户：复核员（用于自我评估和校准）。

### 核心功能与界面描述
* **页面布局与导航**
  * 页面布局：采用典型的报表布局，"全局筛选器+KPI指标卡+多模块分析图表/列表"。
  * 页面标题："复核工作分析报告"。
  * 副标题："从工作量、复核结果、AI差异三个核心维度，全面评估和监控复核团队..."。
  * 页面图标：UserCheck（带对勾的用户图标），代表人工审核。
* **统一搜索筛选器**
  * 目的：允许管理者对复核数据进行多维度筛选。
  * 筛选字段：
    * 时间范围：定义报告的统计周期。
    * 复核员：可以查看单个或全体复核员的数据。
    * 被检班组/团队：可以分析针对特定团队的复核情况。
    * 触发复核原因：可以筛选不同原因（如"低分触发"、"随机抽检"）进入复核流程的数据，用于评估不同复核策略的效果。
* **核心KPI指标卡片**
  * 概览复核工作的核心数据。
  * 指标内容：
    * 总复核量：复核团队的总工作量。
    * 待复核量：当前积压的任务量。
    * 日均复核量：复核团队的平均工作效率。
    * 复核率：人工审核在总质检量中的占比。
    * AI结果纠错率：核心价值指标。指复核分数与AI初检分数不一致的比例，直接衡量了人工复核环节对AI结果的修正价值。
    * 复核后合格率：经过人工确认后的最终合格率，通常被认为是更准确的服务质量指标。
* **复核工作量概览（左右双图表布局）**
  * 左侧图表 - 复核任务完成趋势 (BarChart): 以柱状图展示每日或每周完成的复核任务数量，直观反映复核团队的工作量波动。
  * 右侧图表 - 复核后成绩分布 (PieChart): 以饼图或环形图展示经过人工复核后，最终得分的分布情况（如90-100分占比、80-89分占比等）。帮助管理者了解经过人工校准后，真实的服务质量水平分布。
* **复核结果分析（左右双列表布局）**
  * 左侧列表 - 复核后高频失分规则 TOP 10 (RankingCard): 列出在人工复核确认的案例中，最常出现的失分规则。这比单纯的AI统计结果更可靠，更能反映真实存在的问题。
  * 右侧列表 - 复核后严重错误规则 TOP 10 (RankingCard): 列出在人工复核中确认的、最常见的严重违规项。
* **AI与人工复核差异洞察（核心分析区）**
  * 这是本报告最核心的部分，旨在深入分析AI与人的差异。
  * 左侧卡片 - 分数调整分析:
    * 内容：统计在所有复核案例中，"分数调增"、"分数调减"、"分数不变"的案例数量、占比，以及平均调整的分值。
    * 价值：如果"分数调增"或"分数调减"的比例很高，说明AI的评分标准可能与人的期望存在系统性偏差（过严或过松）。
  * 右侧卡片 - 最大差异规则排行 TOP 10 (RankingCard):
    * 内容：列出那些AI评分与人工评分差异最大的规则。
    * 价值：直接定位了AI最不擅长、最容易出错的质检点。例如，如果"服务热情度评估"这条规则差异最大，说明AI在理解人类微妙情绪方面还有待提升。这些数据是AI模型迭代优化的最宝贵输入。
* **复核员一致性快照（表格）**
  * 功能：对比不同复核员的打分习惯，评估复核团队内部标准的一致性。
  * 表格列定义：
    * 复核员
    * 复核量
    * 平均复核分数：如果某个复核员的平均分显著高于或低于其他人，可能说明其打分尺度存在偏差。
    * AI结果纠错率：如果某个复核员的纠错率特别高或特别低，也需要关注其打分标准。
    * 一致性评估：系统可以根据上述指标综合给出一个评估（如"优秀"、"良好"、"需关注"）。
* **典型差异通话记录（表格）**
  * 功能：列出AI评分与人工复核评分差异最大的几个典型案例。
  * 内容：包含记录ID、坐席、AI得分、复核后得分、分数差异、复核员等。
  * 交互：点击可跳转到详情页，让管理者可以直接研究这些"疑难杂症"，以决策是优化AI，还是统一复核员的认知。
* **核心价值**
  * 聚焦人工环节：整个报告的所有数据都围绕"人工复核"这一环节展开。
  * 评估AI效能：通过对比AI与人的差异，量化地评估了当前AI系统的准确性和可靠性。
  * 驱动双向优化：
    * 优化AI："最大差异规则排行"和"典型差异通话记录"为AI模型训练提供了高质量的标注数据和优化方向。
    * 优化人："复核员一致性快照"帮助管理者发现团队内部的尺度不一问题，通过培训和讨论来统一标准。
  * 数据闭环：将人工复核这一成本投入，转化为优化AI和提升管理水平的数据资产，形成了价值闭环。

---

## 4.6.4. 坐席申诉洞察报告

### 功能目标
* **提供一个专注于坐席申诉数据的专题分析报告。**
* **核心目标是从坐席的"反对票"中反向挖掘和洞察问题，而非仅仅统计申诉情况。**
* **帮助管理者检验质检的公平性、规则的合理性和AI的准确性。高申诉率和高申诉成功率往往是系统或管理中存在问题的明确信号。**
* **洞察团队文化和坐席对质检体系的接受度。**

### 目标用户
* 主要用户：质检主管。

### 核心功能与界面描述
* **页面布局与导航**
  * 页面布局：采用典型的报表布局，"全局筛选器+KPI指标卡+趋势分析+排行榜"。
  * 页面标题："坐席申诉洞察报告"。
  * 副标题："监控申诉情况，保障质检公平性，并从申诉数据中反向挖掘有争议的AI规则或存在标准差异的复核环节"。
  * 页面图标：MessageSquareX（带叉号的对话框），直观地表示对结果的异议和分析。
* **统一搜索筛选器**
  * 筛选字段：时间范围、班组/团队、坐席。
* **申诉概览KPI指标卡片**
  * 以最直观的方式展示申诉工作的整体情况。
  * 指标内容：
    * 总申诉数：周期内发起的申诉总数。
    * 申诉率：申诉数占总质检量的比例。这个指标可以反映坐席对质检结果的整体信服度。
    * 申诉成功率：这是一个核心诊断指标。申诉成功数占总申诉数的比例。如果成功率过高，强烈暗示AI规则或人工复核标准存在严重问题。
* **申诉趋势分析（组合图表）**
  * 图表标题："申诉趋势分析"。
  * 内容：使用一个组合图表 (Composed Chart)，在一个图表中同时展示两条趋势线：
    1. 申诉数量 (Bar Chart - 柱状图)：展示每日或每周的申诉数量。
    2. 申诉成功率 (Line Chart - 折线图)：展示同期的申诉成功率趋势。
  * 价值：管理者可以分析申诉量和成功率的波动关系。例如，某周申诉量和成功率同时飙升，可能与新上线的一条有争议的规则有关。
* **问题定位分析（核心洞察区，左右双卡片布局）**
  * 这是本报告最具洞察力的部分，直接回答了"为什么申诉"和"谁在申诉"这两个核心问题。
  * 左侧卡片 - 高申诉成功率规则排行 (HighAppealSuccessRuleRanking):
    * 标题："高申诉成功率规则排行"。
    * 副标题："指向AI最不准确或定义最模糊的规则"（这个副标题非常精妙，直指问题本质）。
    * 内容：以列表形式，列出那些被申诉并且最终申诉成功次数最多的规则。每一行包含规则名称、申诉次数、申诉成功次数和申诉成功率。
    * 价值：这直接定位了系统中"最不靠谱"的规则。排名靠前的规则是需要立即被审核、优化甚至禁用的首要目标。
  * 右侧卡片 - 高申诉率团队/坐席排行 (HighAppealRateRanking with Tabs):
    * 标题："高申诉率团队/坐席排行"。
    * 副标题："反映团队文化或对质检结果的接受度"。
    * 内容：这是一个带Tab切换的排名榜。
      * 团队排行 (Tab)：按"申诉率"（申诉次数/团队总质检数）对团队进行排名。
      * 坐席排行 (Tab)：按"申诉率"对个人进行排名。
    * 价值：
      * 高申诉率的团队可能存在普遍的异议文化，或者该团队的业务场景与通用质检标准存在冲突，需要班组长介入沟通。
      * 高申诉率的个人可能需要特别关注，分析其申诉是合理的还是习惯性的。
* **核心交互与价值**
  * 反向审查：整个报告的核心逻辑是从"结果"（申诉）反推"原因"（规则问题、管理问题）。这是一种非常高效的问题发现机制。
  * 聚焦"痛点"：报告的设计直指问题核心。"高申诉成功率规则"直接暴露了系统短板，"高申诉率排行"则揭示了管理上的潜在问题。
  * 驱动系统和管理优化：
    * 优化系统：根据"高申诉成功率规则排行"，质检主管可以去质检规则管理页面，对这些有争议的规则进行修改、补充说明，或者用更准确的算子（如大模型）进行重构。
    * 优化管理：根据"高申诉率排行"，管理者可以与相关团队的班组长沟通，了解背后的原因，进行针对性的培训或标准解读，以达成共识。
    * 建立信任：通过认真分析和回应申诉数据，管理者向团队表明了对公平性的重视，有助于建立员工对质检体系的长期信任。

---

## 4.6.5. 历史预警查询

### 功能目标
* **提供一个系统内所有非活跃预警数据的统一查询入口。**
* **为管理者提供一个强大的工具，用于审计、复盘和深度分析那些已经超过"活跃期"的历史预警事件。**
* **与追求"实时性"的预警中心不同，本页面强调查询的深度、广度和数据的完整性。**
* **满足对特定时间段、特定坐席或特定类型风险事件进行批量回顾和统计的复杂查询需求。**

### 目标用户
* 主要用户：质检主管、合规部门人员、需要进行事后调查的管理者。

### 核心功能与界面描述
* **页面布局与导航**
  * 页面布局：采用标准的"头部+强大筛选器+结果列表+分页"的数据查询页面布局。
  * 页面标题："历史预警查询"。
  * 副标题："查询和分析所有已归档的历史预警记录"。
  * 页面图标：History（历史图标）。
  * 核心操作：导出查询结果，将复杂的查询结果导出为文件（CSV/Excel），用于离线分析或归档。
* **统一搜索筛选器**
  * 这是该页面的核心和灵魂，提供了系统中最全面的预警筛选维度。
  * 筛选字段：
    * 时间范围：必填，用于限定查询的时间区间。
    * 预警信息：
      * 触发规则、预警等级。
    * 通话信息：
      * 坐席/班组。
    * 处置信息：
      * 处置状态：下拉选择，如被忽略(已读) / 已创建跟进任务。
      * 跟进任务状态（如果创建了跟进任务）：下拉选择，如已完成 / 处理中 / 已逾期。
    * 关键词搜索：一个文本输入框，允许在预警的上下文摘要中进行全文关键字搜索。例如，搜索包含"退款"的所有历史预警。
* **历史预警列表表格 (Table)**
  * 功能：以表格形式展示所有满足筛选条件的预警记录，强调信息的完整性和可追溯性。
  * 表格列定义：
    * 序号
    * 预警ID
    * 触发时间
    * 触发规则
    * 预警等级
    * 关联坐席/班组
    * 处置状态：清晰地显示该预警的最终处置方式。
    * 跟进任务状态：显示其关联的跟进任务的当前状态。
    * 操作：提供一个"查看详情"的图标按钮，点击后打开预警详情抽屉。
* **分页组件**
  * 由于查询结果可能非常庞大，分页功能是必不可少的。
* **核心交互与价值**
  * 探索性查询：与其他目标明确的页面不同，此页面的主要价值在于其探索性。管理者可以用它来验证某个假设、调查某个特定事件或进行深度的数据挖掘。例如：
    * "查询上一季度所有与'合规风险'相关的、且最终未创建跟进任务的预警，以评估是否存在风险管理漏洞。"
    * "查询坐席小张上个月所有触发了'客户情绪激动'的预警，以评估其情绪控制能力。"
  * 数据审计与追溯：它是系统最全面的审计工具。任何一条预警记录，无论来自哪个任务、哪个计划，都可以在这里被找到并追溯其完整的生命周期。这对于合规审查和事后责任认定至关重要。
  * 权限隔离：页面设计巧妙地通过动态显示/隐藏筛选条件和表格列来实现不同角色的数据权限隔离。坐席只能查自己的数据，班组长能查本组的，而主管拥有全局查询权限，这保证了数据的安全性和合规性。
  * 数据导出：导出功能使得用户可以将系统内的数据与外部工具（如 Excel, Tableau, Python脚本）结合，进行更复杂的自定义分析和可视化。
* **与其他模块的关联**
  * 实时预警中心：是本页面的数据来源。当实时预警超过"活跃期"（如24小时）后，会自动"沉淀"到本模块可查询的历史数据库中。
  * 预警详情抽屉：本页面与实时预警中心共用同一个可复用的"预警详情抽屉"组件，用于展示单条预警的完整信息，保持了体验的一致性。

---

## 4.6.6. 质检明细查询

### 功能目标
* **提供系统内最强大、最灵活的原始数据查询入口。**
* **允许用户跨任务、跨时间、跨团队，根据任意组合的条件来查询系统中任何一条通话的质检明细记录。**
* **与"质检任务详情页"不同，后者是围绕一个"任务批次"来查看数据，而本页面则完全打破了任务的边界，满足各种临时的、复杂的、探索性的数据查询和审计需求。**

### 目标用户
* 主要用户：质检主管、班组长、合规审计人员。
* 次要用户：客服坐席（查询自己的历史记录）。

### 核心功能与界面描述
* **页面布局与导航**
  * 页面布局：采用标准的"头部+强大筛选器+结果列表+分页"的数据查询页面布局。
  * 页面标题（根据角色动态变化）：
    * 质检主管视角："质检明细查询"。
    * 班组长视角："团队质检成绩"。
    * 坐席视角："我的质检成绩"。
  * 副标题："查看和管理质检成绩明细信息"。
  * 页面图标：Search（搜索图标），直观地表明其查询功能。
  * 核心操作：导出数据 (Download图标)，允许用户将当前查询结果导出为文件（如JSON或CSV），用于离线分析或归档。
* **统一搜索筛选器 (核心)**
  * 这是该页面的核心和灵魂，提供了系统中最全面的筛选维度。
  * 筛选字段（根据角色动态显示/隐藏）：
    * 基础信息：记录编号、所属任务、客户号码。
    * 人员信息：坐席（姓名/工号，班组长和主管可见）、所属班组（主管可见）。
    * 时间信息：通话开始时间（支持日期时间范围选择）。
    * 分数信息：最终得分（支持数值范围查询）。
    * 结果信息：
      * 质检结果：下拉选择"合格"、"不合格"。
      * 申诉状态：下拉选择"可申诉"、"申诉中"、"已处理"、"已过期"。
      * 申诉结果：下拉选择"成功"、"失败"。
  * 交互：
    * 用户可以通过组合这些筛选条件，构建出非常复杂的查询逻辑。例如：
      * 查询"A组"在"上个月"所有"最终得分低于70分"且"申诉失败"的记录。
      * 查询坐席"张三"所有由"营销质检任务_1"产生的记录。
      * 查询所有与客户号码"138********"相关的通话记录。
* **查询结果表格 (Table)**
  * 布局：以详细的表格形式展示所有满足筛选条件的质检记录。
  * 表格列（根据角色动态显示/隐藏）：
    * 序号、记录编号、所属任务
    * 坐席（班组长和主管可见）
    * 所属班组（主管可见）
    * 客户号码（脱敏）
    * 通话开始时间、通话时长
    * 最终得分：核心指标，同样通过Tooltip展示分数演进过程（AI -> 复核 -> 申诉）。
    * 质检结果："合格"/"不合格"徽章。
    * 申诉信息：一个复合信息列，用徽章和文字清晰展示申诉状态、结果和关键时间（如申诉有效期、处理时间）。
    * 操作：
      * [查看详情 (Eye图标)]：所有角色都有此权限，点击跳转到[多模式会话详情页]，深入了解该次通话的全部细节。
      * [发起申诉 (AlertTriangle图标)]：仅在坐席视角下，对符合条件的记录显示此按钮，提供申诉入口。
* **分页组件**
  * 由于查询结果可能非常庞大，分页功能是必不可少的。
* **核心交互与价值**
  * 探索性查询：与其他目标明确的页面不同，此页面的主要价值在于其探索性。管理者可以用它来验证某个假设、调查某个特定事件或进行深度的数据挖掘。
  * 数据审计与追溯：它是系统最全面的审计工具。任何一条记录，无论来自哪个任务、哪个计划，都可以在这里被找到并追溯其完整的生命周期。这对于合规审查和事后责任认定至关重要。
  * 权限隔离：页面设计巧妙地通过动态显示/隐藏筛选条件和表格列来实现不同角色的数据权限隔离。坐席只能查自己的数据，班组长能查本组的，而主管拥有全局查询权限，这保证了数据的安全性和合规性。
  * 数据导出：导出功能使得用户可以将系统内的数据与外部工具（如 Excel, Tableau, Python脚本）结合，进行更复杂的自定义分析和可视化。
* **与"质检成绩页"的区别与联系**
  * 在产品信息架构中，可能还有一个简化的"质检成绩页"。本页面可以看作是那个页面的"高级版"或最终形态。
  * "质检成绩页"可能只提供基础的筛选，而本"质检明细查询"页面提供了最全面的筛选维度，是终极的查询工具。在实际开发中，两者可以合并为一个页面，通过权限控制其复杂性。 