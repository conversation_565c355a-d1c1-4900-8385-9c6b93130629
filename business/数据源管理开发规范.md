# 管理页面100%样式复刻规范

> **⚠️ 强制性要求**
>
> 所有新开发的管理页面必须100%复刻数据源管理页面的样式。任何视觉差异都是不可接受的。

## 核心要求

**唯一目标：视觉完全一致**
- 新页面与数据源管理页面在视觉上无法区分
- 浅色模式和暗色模式都必须完全一致
- 所有间距、颜色、字体、阴影效果都必须相同

## 1. CSS变量系统（必须完全复制）

**⚠️ 严禁使用Element Plus默认变量，必须使用以下自定义变量：**

```css
/* 必须完全复制的CSS变量系统 */
:root {
  --bg-color: #f5f7fa;
  --card-bg-color: #ffffff;
  --text-primary: #303133;
  --text-secondary: #606266;
  --text-tertiary: #909399;
  --border-color: #e4e7ed;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --hover-bg-color: #f0f9ff;
  --drawer-footer-bg: #fafafa;
}

/* 暗色模式必须完全一致 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-color: #1a1a1a;
    --card-bg-color: #2d2d2d;
    --text-primary: #e5eaf3;
    --text-secondary: #a3a6ad;
    --text-tertiary: #6c6e72;
    --border-color: #414243;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --hover-bg-color: #3a3a3a;
    --drawer-footer-bg: #262626;
  }
}
```

**❌ 错误做法：**
```css
var(--el-bg-color)
var(--el-text-color-primary)
var(--el-border-color-light)
```

**✅ 正确做法：**
```css
var(--bg-color)
var(--text-primary)
var(--border-color)
```

## 2. 完整样式模板（直接复制使用）

**新页面开发时，直接复制以下完整模板：**

```vue
<style scoped>
/* CSS变量定义 - 必须完全复制 */
:root {
  --bg-color: #f5f7fa;
  --card-bg-color: #ffffff;
  --text-primary: #303133;
  --text-secondary: #606266;
  --text-tertiary: #909399;
  --border-color: #e4e7ed;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --hover-bg-color: #f0f9ff;
  --drawer-footer-bg: #fafafa;
}

/* 暗色模式 - 必须完全复制 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-color: #1a1a1a;
    --card-bg-color: #2d2d2d;
    --text-primary: #e5eaf3;
    --text-secondary: #a3a6ad;
    --text-tertiary: #6c6e72;
    --border-color: #414243;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --hover-bg-color: #3a3a3a;
    --drawer-footer-bg: #262626;
  }
}

/* 页面容器 */
.your-page-management {
  padding: 24px;
  background: var(--bg-color);
  min-height: 100vh;
}

/* 页面头部 - 必须完全复制 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: var(--card-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 8px var(--shadow-color);
}

.header-content {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.title-icon {
  margin-right: 12px;
  color: #409EFF;
}

.page-description {
  color: var(--text-secondary);
  font-size: 14px;
  margin: 0;
  line-height: 1.5;
}

.header-actions {
  margin-left: 24px;
}

/* 筛选区域 - 必须完全复制 */
.filter-section {
  margin-bottom: 16px;
}

.filter-card {
  border: none;
  box-shadow: 0 2px 8px var(--shadow-color);
}

/* 表格区域 - 必须完全复制 */
.table-section {
  margin-bottom: 24px;
}

.table-card {
  border: none;
  box-shadow: 0 2px 8px var(--shadow-color);
}

/* 分页样式 - 必须完全复制 */
.pagination-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
  padding: 16px 0;
}

.pagination-wrapper :deep(.el-pagination) {
  --el-pagination-font-size: 14px;
}

.pagination-wrapper :deep(.el-pagination__total) {
  margin-right: auto;
  color: var(--el-text-color-regular);
}

.pagination-wrapper :deep(.el-pagination__sizes) {
  margin-left: 16px;
}

.pagination-wrapper :deep(.el-pagination__jump) {
  margin-left: 16px;
}

/* 抽屉样式 - 必须完全复制 */
.drawer-content {
  padding: 24px;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  border-top: 1px solid var(--border-color);
  background: var(--drawer-footer-bg);
}

/* 表格内容样式 */
.name-cell {
  display: flex;
  align-items: center;
}

.type-icon {
  margin-right: 8px;
  font-size: 16px;
}

.name-text {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .your-page-management {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: stretch;
  }

  .header-actions {
    margin-left: 0;
    margin-top: 16px;
  }
}
</style>
```

## 3. 快速检查清单

**开发完成后，请逐项检查以下内容：**

#### CSS变量检查
- [ ] 使用自定义CSS变量，不使用Element Plus变量
- [ ] 暗色模式变量定义完整
- [ ] 所有颜色值与数据源管理页面完全一致

#### 页面布局检查
- [ ] 页面容器：`padding: 24px; background: var(--bg-color)`
- [ ] 页面头部：卡片样式，包含padding、background、border-radius、box-shadow
- [ ] 筛选区域：`margin-bottom: 16px`（不是24px）
- [ ] 表格区域：`margin-bottom: 24px`
- [ ] 分页组件：`justify-content: flex-end`（不是center）

#### 视觉效果检查
- [ ] 表格头部有深色背景
- [ ] 卡片有阴影效果：`box-shadow: 0 2px 8px var(--shadow-color)`
- [ ] 文字颜色使用自定义变量
- [ ] 图标颜色与数据源管理一致

#### 主题适配检查
- [ ] 浅色模式下与数据源管理页面完全一致
- [ ] 暗色模式下与数据源管理页面完全一致
- [ ] 主题切换时无闪烁或异常

## 4. 验证方法

```bash
# 1. 打开数据源管理页面（标准）
http://localhost:5173/5-1

# 2. 打开新开发页面（待验证）
http://localhost:5173/your-page

# 3. 切换浏览器标签页进行对比
# 4. 切换系统主题模式再次对比
```

**通过标准：**
- 用户无法区分两个页面的视觉差异
- 所有检查项都已完成
- 在不同主题模式下都保持一致

## 5. 常见错误

**错误1：使用Element Plus默认变量**
```css
/* ❌ 错误 */
background: var(--el-fill-color-lighter);

/* ✅ 正确 */
background: var(--card-bg-color);
```

**错误2：页面头部缺少卡片样式**
```css
/* ❌ 错误 - 平铺布局 */
.page-header {
  margin-bottom: 24px;
}

/* ✅ 正确 - 卡片布局 */
.page-header {
  margin-bottom: 24px;
  padding: 24px;
  background: var(--card-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 8px var(--shadow-color);
}
```

**错误3：分页组件居中对齐**
```css
/* ❌ 错误 */
.pagination-wrapper {
  justify-content: center;
}

/* ✅ 正确 */
.pagination-wrapper {
  justify-content: flex-end;
}
```

## 6. 开发流程

**新页面开发标准流程：**

1. **复制模板** - 使用第2节完整样式模板
2. **功能开发** - 实现具体的业务功能
3. **样式验证** - 使用第3节检查清单进行验证
4. **对比测试** - 使用第4节验证方法确认一致性

**质量要求：**
- 任何视觉差异都必须修复
- 不允许"差不多"的情况
- 必须在浅色和暗色模式下都验证

## 总结

**唯一目标：100%视觉一致性**

所有新开发的管理页面都必须与数据源管理页面在视觉上完全一致，这是强制性要求，不是建议。