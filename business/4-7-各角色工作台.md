# 4.7. 各角色工作台

为了实现**“角色中心”**的设计哲学，避免让用户在复杂的功能模块中迷失，系统为每一类核心用户都提供了一个专属的、聚合其最关心信息和最高频操作的**“工作台”**或**“首页”**。本章节将详细阐述这些个性化页面的设计，以及一些所有角色通用的核心页面。

---

## 4.7.1. 主管首页

### 功能目标
* **为质检主管提供一个360度的宏观业务视图，帮助其全面监控质检体系的健康度、效率、质量和风险。**
* **将最重要、最需关注的全局数据和待办事项，以最直观的方式呈现在首页，实现“一屏看全貌”。**
* **作为主管所有工作的起点，提供到各个核心管理模块和分析报表的快速入口。**
* **赋能主管进行快速的战略判断和决策。**

### 目标用户
* 主要用户：质检主管（或拥有最高管理权限的用户）。

### 核心功能与界面描述 (Dashboard布局)
* **页面头部**
  * 标题：“质检主管视角”。
  * 副标题：显示主管的个人信息及管理范围，例如：“欢迎回来，李主管 | 质检部 | 管理团队：3个”。
  * 图标：Shield（盾牌图标），象征着质量保障和风险管控。
* **核心绩效指标 (MetricCard 网格布局)**
  * 展示整个质检体系最高维度的宏观KPI，分为两行，逻辑上聚合了所有团队和坐席的数据：
    * 第一行（运营规模与效率）：
      * 总质检量：整个系统完成的质检通话总数，反映AI的覆盖面和工作效率。
      * 总复核量：所有复核员完成的人工审核总数，反映人工投入成本。
      * 质检平均分：全体坐席的平均得分，是衡量整体服务质量的核心基线。
      * 质检合格率：全体坐席的合格率，反映整体服务质量的达标情况。
    * 第二行（风险与AI效能）：
      * 申诉率：整体申诉情况，是衡量质检结果公信力和规则合理性的关键指标。
      * 申诉成功率：反映申诉处理的有效性，高成功率可能指向AI或复核标准的问题。
      * AI质检准确率：评估AI引擎价值的核心指标，显示AI判断与人工终审的一致性，直接关系到系统的可靠性和投入产出比。
* **成绩趋势分析 (折线图)**
  * 图表标题：“成绩趋势分析”。
  * 内容：在一个组合图表中，用两条折线分别展示近期整体平均分和整体合格率的变化趋势。
  * 价值：主管可以从宏观上监控服务质量的波动，并与公司层面的战略调整、市场活动或重大事件进行关联分析。
* **排名统计 (左右双表格布局)**
  * 横向对比不同团队和个人的表现，发现标杆和洼地。
  * 左侧卡片 - 团队成绩排名 Top 5 (Table)：
    * 以表格形式清晰展示表现最好的前5个团队，列出团队名称、平均分、合格率、成员数等关键信息。
  * 右侧卡片 - 坐席成绩排名 Top 5 (Table)：
    * 展示全公司范围内表现最优秀的坐席，并标注其所属团队，为跨团队的标杆分享和激励提供依据。
* **错误分析 (左右双卡片布局)**
  * 与班组长视角类似，但数据范围是全公司，用于发现系统性的、普遍存在的风险点。
  * 左侧卡片 - 严重错误项 Top 5：
    * 统计在所有通话中，触犯次数最多的严重违规项。这是公司层面的核心风险点，需要重点关注和整治。
  * 右侧卡片 - 高频失分项 Top 5：
    * 统计导致所有坐席失分最多的规则，这直接指向了公司级培训材料和SOP（标准作业程序）需要优化的方向。
* **通知公告与待处理申诉 (左右双卡片布局)**
  * 左侧卡片 - 通知公告：
    * 展示最高级别的通知，如系统公告、质检标准重大更新、需要主管关注的任务预警（例如，申诉案件积压过多）。
  * 右侧卡片 - 待处理申诉：
    * 为质检主管定制的高优先级待办列表。展示最新或最紧急的待处理申诉案件，每条包含记录ID、申诉坐席、AI得分、申诉理由等简要信息。
    * 交互：每条记录末尾都有一个“开始处理”按钮，点击直接跳转到申诉处理页面或对应的会话详情页，方便主管快速响应。

### 核心交互
* 宏观监控：页面所有数据均为全局聚合数据，为管理者提供“一屏看全貌”的驾驶舱体验。
* 决策支持：所有模块都旨在回答管理者最关心的问题：我们的服务质量怎么样？风险在哪里？谁做得好？谁需要帮助？AI可靠吗？
* 管理入口：通过点击排名、错误项、待处理申诉等，可以无缝跳转到更详细的分析报表或处理页面，实现从“发现问题”到“分析问题”再到“解决问题”的流畅管理闭环。

---

## 4.7.2. 班组长首页

### 功能目标
* **为班组长提供一个管理团队所需的核心数据视图，帮助他们从团队整体和个体成员两个层面快速掌握服务质量状况。**
* **将班组长最关心的团队绩效、成员排名、普遍问题和待办事项聚合在首页，提升日常管理效率。**
* **作为团队管理的起点，为成员辅导、绩效沟通和问题发现提供直接的数据支持和快速入口。**

### 目标用户
* 主要用户：班组长 (Team Leader)。

### 核心功能与界面描述 (Dashboard布局)
* **页面头部**
  * 标题：“班组长视角”。
  * 副标题：显示当前登录班组长的信息，以及其所管理的团队信息，例如：“欢迎回来，李班长 | 客服部-A班组 | 团队成员：12人”。
  * 图标：Users（用户组图标），代表团队视角。
  * 徽章：显示团队的关键状态，例如其在所有班组中的“排名 #2”。
* **团队核心绩效指标 (MetricCard 网格布局)**
  * 展示团队维度的KPI：
    * 团队平均质检得分：展示整个团队在周期内的平均分，反映团队整体水平。
    * 团队质检合格率：展示团队质检合格的通话占比，衡量团队的整体服务质量达标情况。
    * 团队申诉率：统计整个团队发起的申诉占总质检量的比例，可以反映团队对质检结果的接受度或规则理解的一致性。
    * 团队申诉成功率：统计团队申诉成功的比例，高成功率可能意味着AI规则或复核标准存在问题，需要关注。
* **整体服务质量趋势与团队成员成绩排名 (左右布局)**
  * 左侧 (占2/3宽度) - 整体服务质量趋势 (LineChart)：
    * 图表标题：“成绩趋势分析”。
    * 内容：使用折线图展示团队近期的平均分变化趋势。
  * 右侧 (占1/3宽度) - 团队成员成绩排名 (List/Table)：
    * 标题：“团队成员成绩排名”。
    * 内容：以列表形式展示团队内部成员的绩效排名（Top 5 和 Bottom 5）。每位成员的信息包括：
      * 排名（奖杯或星级图标区分前三名）。
      * 姓名及个人表现趋势（上升/下降图标）。
      * 个人平均分、合格率、申诉次数等。
* **团队错误分析统计 (左右双卡片布局)**
  * 左侧卡片 - 团队严重错误项 Top 5：
    * 统计整个团队触犯最多次的“严重”级别规则。
  * 右侧卡片 - 团队高频失分项 Top 5：
    * 统计导致团队整体失分最多的规则。
* **通知公告与团队近期质检记录 (左右双卡片布局)**
  * 左侧卡片 - 通知公告：
    * 展示与班组长和其团队相关的通知。
  * 右侧卡片 - 近期质检记录：
    * 展示团队成员最近的质检记录列表。
    * 信息包括：记录ID、坐席姓名、质检结果、最终得分、申诉状态等。
    * 交互：点击可跳转到该次通话的质检详情页。

### 核心交互
* 数据聚合与下钻：页面展示的是聚合后的团队数据，但通过点击“团队成员排名”或“近期质检记录”，班组长可以方便地下钻到个体成员的详细数据。
* 问题定位：通过“错误分析”模块快速定位团队的共性问题，为管理决策提供支持。
* 团队管理：班组长可以根据页面数据，安排针对性的团队培训、一对一辅导或绩效沟通。

---

## 4.7.3. 复核员首页

### 功能目标
* **为质检复核员提供一个清晰、高效的个人工作台（Dashboard），帮助其快速了解自己的工作负荷、效率和质量。**
* **提供快速进入待办任务列表的入口，减少不必要的操作步骤。**
* **强调对个人工作状态的监控，而非对他人的绩效评估，让复核员能专注于自身的工作。**

### 目标用户
* 主要用户：复核员 (Reviewer)。

### 核心功能与界面描述 (Dashboard布局)
* **页面头部**
  * 标题：“复核员视角”。
  * 副标题：显示复核员的个人信息，例如：“欢迎回来，李复核 | 质检部-复核组”。
  * 图标：UserCheck（带对勾的用户图标），象征着审核与确认。
  * 徽章：显示复核员的关键状态，如“在线”。
* **核心工作指标 (MetricCard 网格布局)**
  * 展示衡量复核员工作量和核心价值的KPI：
    * 复核总量：周期内完成的复核任务总数。
    * 待复核量：当前分配但尚未完成的任务数。
    * 日均复核量：平均工作效率。
    * AI结果纠错数：展示复核员修正AI初检结果的次数。
* **工作量趋势分析 (折线图)**
  * 图表标题：“工作量趋势”。
  * 内容：双折线图，展示每日完成复核任务数量和AI纠错数量。
  * 交互：鼠标悬浮时，Tooltip会同时显示当天的“完成复核数”和“纠错数量”。
* **通知公告 (卡片布局)**
  * 聚合与复核员工作直接相关的信息。
* **待复核任务列表 (卡片布局)**
  * 标题：“待复核任务”。
  * 展示最新或最高优先级的几条待复核任务。
  * 每条任务包含：记录ID、被检坐席、通话信息、AI初检分数、质检类型/触发原因。
  * 交互：每条任务末尾有“开始复核”按钮，点击后直接跳转到[多模式会话详情页]。

### 核心交互
* 任务驱动：页面设计以“任务”为中心，所有模块最终都指向引导复核员去处理待办任务。
* 效率与价值可视化：通过工作量趋势图和“AI结果纠错数”指标，让复核员能直观地看到自己的工作效率和为系统带来的价值。
* 快速访问：“待复核任务”列表提供了最直接的工作入口。

---

## 4.7.4. 客服坐席首页

### 功能目标
* **为客服坐席提供一个关于个人工作表现的全面、直观的快照（Dashboard）。**
* **帮助坐席快速了解自己的绩效水平、发现潜在问题、接收关键通知，并明确改进方向。**
* **将冰冷的质检分数转化为帮助坐席成长的工具，提升其对质检工作的认同感和参与度。**

### 目标用户
* 主要用户：客服坐席 (Agent)。

### 核心功能与界面描述 (Dashboard布局)
* **页面头部**
  * 标题：“坐席员视角”。
  * 副标题：显示当前登录坐席的欢迎信息。
  * 图标：User（用户图标），代表个人视角。
  * 徽章：显示坐席的当前状态，如“在线”。
* **核心绩效指标 (MetricCard 网格布局)**
  * 由6个卡片组成的核心数据区：
    * 平均分数、团队排名、质检合格率、一次性通过率、申诉次数、申诉成功率。
    * 每个卡片都包含趋势指示器和与上周期的对比值，并附有帮助图标。
* **成绩趋势分析 (折线图)**
  * 图表标题：“成绩趋势分析”。
  * 内容：平滑折线图，展示坐席在近期的每日质检平均分数变化。
  * 交互：鼠标悬浮在图表节点上，可查看具体某一天的分数。
* **错误分析统计 (左右双卡片布局)**
  * 左侧卡片 - 严重错误项 Top5：
    * 以列表形式展示坐席最常触犯的、定性为“严重”的质检规则。
  * 右侧卡片 - 高频失分项 Top5：
    * 罗列导致坐席失分次数最多的规则。
* **通知公告与近期成绩 (左右双卡片布局)**
  * 左侧卡片 - 通知公告：
    * 展示与坐席相关的最新通知。
  * 右侧卡片 - 近期成绩：
    * 展示最近几条已出分的质检记录列表。
    * 每条记录包含：记录ID、最终得分、质检结果、申诉状态、通话的基本信息。
    * 交互：每条记录都是一个入口，点击可跳转到该次通话的质检详情页。

### 核心交互
* 查看：页面主要以数据展示为主，让坐席一目了然地“看”到自己的表现。
* 跳转：点击“通知公告”或“近期成绩”中的具体条目，可以跳转到对应的任务详情或成绩详情页。
* 了解定义：鼠标悬浮在KPI卡片的帮助图标上，可以了解指标的详细含义。

### 与其他模块的关系
* 本页面是坐席与质检系统交互的主要入口。
* 它是质检成绩页和多模式会话详情页的上游和摘要。
* 它是坐席发起申诉流程的潜在起点。

---

## 4.7.5. 质检成绩页

### 功能目标
* **提供一个多角色复用的质检成绩查询中心。**
* **根据当前登录用户的角色，提供一个权限范围内、可追溯、可筛选的所有历史质检成绩记录列表。**
* **比首页的“近期成绩”列表更全面，是一个用于正式查询和回顾的工具。**
* **为坐席提供发起申诉的入口，为主管提供模拟不同角色视图的能力。**

### 目标用户
* 所有核心角色：客服坐席、班组长、质检主管。

### 核心功能与界面描述
* **页面头部 (动态变化)**
  * 标题与副标题：
    * 坐席视角: “我的质检成绩”
    * 班组长视角: “团队质检成绩”
    * 主管视角: “质检成绩管理”
  * 图标：BarChart3（条形图图标）。
  * 徽章（动态变化）：根据当前角色显示不同的标签。
* **角色切换器 (Supervisor/Admin Only)**
  * 允许高权限用户在不同视角之间自由切换。
* **统一搜索筛选器 (根据角色动态显示)**
  * 提供强大的、多维度的查询工具。
  * 筛选字段：记录编号、所属任务、客户号码、通话开始时间范围、最终得分范围、质检结果、申诉状态、申诉结果、坐席、所属班组。
* **成绩记录表格 (Table) (核心)**
  * 以详细的表格形式展示所有符合条件的质检记录。
  * 表格列（根据角色动态显示）：序号、记录编号、所属任务、坐席、所属班组、客户号码、通话开始时间、通话时长、最终得分、质检结果、申诉信息、操作。
  * 操作：
    * [查看详情 (Eye图标)]
    * [发起申诉 (仅坐席)]
* **分页组件**
  * 用于浏览大量的历史成绩数据。

### 核心交互
* 多维度查询：组合不同的筛选条件，实现灵活的数据查询。
* 数据追溯：点击“查看详情”进行深度追溯。
* 角色模拟：主管可通过角色切换器，快速代入班组长或坐席的视角。
* 申诉入口：为坐席提供便捷的申诉入口。

---

## 4.7.6. 通知中心

### 功能目标
* **为系统内所有角色的用户提供一个消息和通知的聚合中心。**
* **确保所有与用户工作相关的关键信息不会被遗漏，并支持对历史消息的追溯。**
* **提供一个统一的、可管理的界面，来接收、查看和处理各类系统通知。**
* **通过清晰的分类和状态管理，帮助用户高效地处理信息，避免信息过载。**

### 目标用户
* 所有核心角色：质检主管、班组长、复核员、客服坐席。

### 核心功能与界面描述
* **页面布局与导航**
  * 经典邮箱式左右双栏布局。左侧为分类导航，右侧为通知列表和内容。
  * 页面标题：“通知中心”。
  * 副标题：“查看和管理系统通知消息”。
  * 页面图标：Bell（铃铛图标）。
  * 页面徽章：动态显示未读消息的总数。
  * 快捷操作：全部已读、清空通知。
* **左侧 - 通知分类导航**
  * 分类项：全部通知、任务提醒、结果通知、系统公告。
  * 每个分类旁边用数字角标显示未读通知数量。
* **右侧 - 主内容区**
  * 统一搜索筛选器：搜索内容、读取状态、时间范围。
  * 批量操作栏：全选、标记为已读、标记为未读、删除选中。
  * 通知列表 (List)：
    * 每一条通知都是一个独立的卡片或列表项。
    * 单条通知包含：复选框、已读/未读状态点、通知类型徽章、标题和内容摘要、时间戳、快捷操作。
    * 交互：点击通知标题或内容区域自动标记为已读，并可跳转到详情页。
    * 未读通知有更醒目的背景色或字体样式。
  * 分页组件：用于浏览大量的历史通知。

### 核心交互与价值
* 信息聚合与分发：统一管理所有消息，通过分类和筛选高效处理。
* 任务驱动跳转：点击通知可直接跳转到需要操作的页面。
* 状态管理：清晰管理每条通知的“已读/未读”状态。
* 批量处理：高效的批量操作能力，方便快速清理或标记大量通知。 